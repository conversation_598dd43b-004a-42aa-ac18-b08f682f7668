.features-card-grid { @apply mt-12 md:mt-16; }

.features .section-heading { @apply mb-12 md:mb-16; }

.features-item { @apply flex flex-wrap gap-4 items-center; }
.features-item .features-icon { @apply text-2xl text-gray-400; }
.features-item .features-title { @apply flex-1 m-0; }
.features-item .features-text { @apply w-full; }
.features-item .features-text.content_section p { @apply m-0; }

#features-2 .card-title { @apply mt-4; }
#features-3 .features-card-grid { @apply mt-0 md:mt-0; }
#features-3 .features-image { @apply w-[48rem] max-w-none rounded-xl shadow-xl ring-1 ring-gray-400/10 sm:w-[57rem]; }
#features-4 .card-title { @apply mt-3; }
#features-4 .card-icon { @apply bg-primary-600 text-white; }

/* TODO: volgende regels moet naar utilities.helper-classes.css, bestands staat atm in andere feature-branch */
.hide-mobile-block { @apply hidden md:block; }
.hide-mobile-flex { @apply hidden md:flex; }
