[staticMenu mainMenu]
code = "main-menu"
==

{% set mainmenuItems = mainMenu.resetMenu(this.theme.navbar.site_primary_menu) %}
{% set halfLength = mainmenuItems|length / 2 %}
{% set midPoint = halfLength|round %}

{% set leftMenu = mainmenuItems|slice(0, midPoint) %}
{% set rightMenu = mainmenuItems|slice(midPoint) %}

<header id="navbar-7" class="navbar">
    <div class="container">
        <div class="navbar-row flex flex-wrap items-center justify-between">
            <nav class="mainmenu">
                {% partial 'page/mainmenus/mainmenu-left' leftMenu=leftMenu %}
            </nav>
            <div class="logo">
                {% partial 'ui/logo/logo_primair' %}
            </div>
            <nav class="mainmenu" x-data="{ open: false }">
                {% partial 'page/mainmenus/mainmenu-right' rightMenu=rightMenu %}
            </nav>
        </div>
    </div>
</header>
