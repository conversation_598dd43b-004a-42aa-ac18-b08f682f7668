<section id="benefits-2" class="benefits" style="background-color: {{ box.background_color }};" data-boxes-container data-rel="boxes-wrapper">
    <div class="container">
        <div class="grid grid-cols-2">
            {% for key,item in box.benefits %}

            {% if key == '0' %}
            <div class="aspect-square">
                <img src="{{ item.img|media|resize(1200, auto, { 'extension': 'webp' }) }}" alt=""
                     class="h-full w-full object-cover">
            </div>
            <div class="flex flex-col justify-center space-y-4 p-16">
                <div class="">
                    <h2 class="">{{ item.title }}</h2>
                </div>
                <div class="">
                    {{ item.content|content }}
                </div>
                <div class="pt-8">
                    {% partial 'ui/button' text="Read articles" size="btn" style="primary" %}
                </div>
            </div>

            {% elseif key == '1' %}

            <div class="flex flex-col justify-center space-y-4 p-16">
                <div class="">
                    <h2 class="">{{ item.title }}</h2>
                </div>
                <div class="">
                    {{ item.content|content }}
                </div>
                <div class="pt-8">
                    {% partial 'ui/button' text="Read articles" size="btn" style="primary" %}
                </div>
            </div>
            <div class="aspect-square">
                <img src="{{ item.img|media|resize(1200, auto, { 'extension': 'webp' }) }}" alt=""
                     class="h-full w-full object-cover">
            </div>

            {% endif %}
            {% endfor %}

        </div>
    </div>
</section>
