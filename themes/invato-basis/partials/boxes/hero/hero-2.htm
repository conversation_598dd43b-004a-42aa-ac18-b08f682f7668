<div id="hero-2" class="hero hero-wide wrapper relative bg-gray-100" data-boxes-container data-rel="boxes-wrapper">
    <div class="container px-0 lg:grid lg:grid-cols-12 lg:gap-x-8">
        <div class="px-6 pb-24 pt-10 sm:pb-32 lg:col-span-7 lg:px-0 lg:pb-56 lg:pt-48 xl:col-span-6">
            <div class="mx-auto max-w-2xl lg:mx-0">

                <div class="hero-title mb-6 lg:mb-12">
                    <h1 class="lead">Welcome to your new wireframe kit</h1>
                </div>
                <div class="hero-text mb-8 lg:mb-16 max-w-3xl mx-auto text-lg text-gray-400">
                    <p>If you’re looking for the latest in wireless headphones, look no further. These are perfect for TV, stereo, home, and cell phone.</p>
                </div>

                <div class="hero-buttons flex">
                    <a href="#" class="btn-2xl btn-primary">Start now!</a>
                </div>

            </div>
        </div>
        <div class="relative lg:col-span-5 lg:-mr-8 xl:absolute xl:inset-0 xl:left-1/2 xl:mr-0">
            <img class="aspect-[3/2] w-full bg-gray-50 object-cover lg:absolute lg:inset-0 lg:aspect-auto lg:h-full" src="{{ 'assets/img/demo/hero-3.jpg' | theme}}" alt="">
        </div>
    </div>
</div>
