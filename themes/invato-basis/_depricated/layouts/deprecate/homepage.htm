description = "Homepagina"

[staticPage]
useContent = 0
default = 0

[staticBreadcrumbs]

[sitePicker]
==
<!doctype html>
<html lang="nl">
    <head>
        {% partial "site/meta" %}
        {% partial "site/scripts_head" %}
        {% partial "page/scripts_head" %}
        {% partial "site/head" %}
        {% styles %}
    </head>
    <body>
        {% partial "site/scripts_body_top" %}
        {% partial "page/scripts_body_top" %}
        {% page  %}
        {% partial "page/navbars/" ~ this.theme.navbar|default('navbar-1') ~ ".htm" %}

        <main class="">

            {% page  %}

            {repeater tab="Content" name="flexible" prompt="Voeg sectie toe" groups="$/../themes/invato-basis/repeaters/sections.yaml" displayMode="builder" titleFrom="name"}
                {% if 'banner_' in fields._group %}
                {% partial "section/banner/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'cta_' in fields._group %}
                {% partial "section/cta/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'feature_' in fields._group %}
                {% partial "section/feature/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'hero_' in fields._group %}
                {% partial "section/hero/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'logos_' in fields._group %}
                {% partial "section/logos/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'snippet_' in fields._group %}
                {% partial "section/snippet/" ~ fields._group ~ ".htm" fields=fields %}
                {% elseif 'text_' in fields._group %}
                {% partial "section/text/" ~ fields._group ~ ".htm" fields=fields %}
                {% else %}
                {% partial "section/" ~ fields._group ~ ".htm" fields=fields %}
                {% endif %}
            {/repeater}

        </main>
        {% if this.theme.navbar == 'navbar-3' %}
        {variable tab="Flyout menu" name="snippet_title" type="text" label="Title" span="left" permissions="developer"}{/variable}
        {variable tab="Flyout menu" name="snippet" type="richeditor" size="huge" label="Snippet" span="left" toolbarButtons="snippets|html" permissions="developer"}{/variable}
        {% endif %}

        {% partial "page/footers/" ~ this.theme.footer|default('footer-1') ~ ".htm" %}

        {% partial "page/cookie-alert" %}

        {repeater tab="Scripts" name="page_scripts_head" label="Scripts in de head" prompt="Script toevoegen" span="row" spanClass="col-sm-4" nameFrom="title"}
            {variable name="title" label="Titel" tab="Scripts" type="text"}{/variable}
            {variable name="script" label="Scripts in head" tab="Scripts" type="codeeditor" size="huge" language"javascript"}{/variable}
        {/repeater}
        {repeater tab="Scripts" name="page_scripts_body_top" label="Scripts in de body (boven)" prompt="Script toevoegen" span="row" spanClass="col-sm-4" nameFrom="title"}
            {variable name="title" label="Titel" tab="Scripts" type="text"}{/variable}
            {variable name="script" label="Scripts in body top" tab="Scripts" type="codeeditor" size="huge" language"javascript"}{/variable}
        {/repeater}
        {repeater tab="Scripts" name="page_scripts_body_bottom" label="Scripts in de body (onder)" prompt="Script toevoegen" span="row" spanClass="col-sm-4" nameFrom="title"}
            {variable name="title" label="Titel" tab="Scripts" type="text"}{/variable}
            {variable name="script" label="Scripts in body bottom" tab="Scripts" type="codeeditor" size="huge" language"javascript"}{/variable}
        {/repeater}


        {% partial "site/foot" %}
        {% partial "page/scripts_body_bottom" %}
        {% partial "site/scripts_body_bottom" %}

        {% scripts %}
        {% framework extras %}

        <script>
            var lazyLoadInstance = new LazyLoad({});
        </script>
    </body>
</html>
