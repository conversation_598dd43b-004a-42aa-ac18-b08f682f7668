jQuery(function(){
    cookieAlert();
    autoCompleteAddress();
    externalLinks();
});

function dropdown() {
    return {
        show: false,
        open() { this.show = true },
        close() { this.show = false },
        isOpen() { return this.show === true },
    }
}

function setCookie(name,value,days) {
    var expires = "";
    if (days) {
        var date = new Date();
        date.setTime(date.getTime() + (days*24*60*60*1000));
        expires = "; expires=" + date.toUTCString();
    }
    document.cookie = name + "=" + (value || "")  + expires + "; path=/";
}

function getCookie(name) {
    var nameEQ = name + "=";
    var ca = document.cookie.split(';');
    for(var i=0;i < ca.length;i++) {
        var c = ca[i];
        while (c.charAt(0)==' ') { c = c.substring(1,c.length) }
        if (c.indexOf(nameEQ) == 0) { return c.substring(nameEQ.length,c.length) }
    }
    return null;
}

function eraseCookie(name) {
    document.cookie = name +'=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
}

cookieAlert = function(){
    var body = $('body');
    var cookieAlert = body.find('#cookieAlert');
    var cookieAccept = cookieAlert.find('#cookieAccept');
    var cookieDismiss = cookieAlert.find('#cookieDismiss');
    var cookieValue = getCookie('cookieAccept');

    if ( !cookieValue ) {
        cookieAlert.show();
    }

    cookieAccept.on('click', function(){
        eraseCookie('cookieAccept');
        setCookie('cookieAccept', 'true', 365);
        cookieAlert.fadeOut(250);
    });

    cookieDismiss.on('click', function(){
        eraseCookie('cookieAccept');
        setCookie('cookieAccept', 'false', 365);
        setCookie('cookieAccept', 'false');
        cookieAlert.fadeOut(250);
    });
}

autoCompleteAddress = function(){
    $('form').on('change', '[name="postcode"]', function(){
        var form = $(this).closest('form');
        var zipcode = form.find('[name="postcode"]').val();
        var housenumber = form.find('[name="huisnummer"]').val();

        if ( zipcode != '' && housenumber != '' ) {
            autoComplete(form,zipcode,housenumber);
        }
    });
    $('form').on('keyup', '[name="huisnummer"]', function(){
        var form = $(this).closest('form');
        var zipcode = form.find('[name="postcode"]').val();
        var housenumber = form.find('[name="huisnummer"]').val();

        if ( zipcode != '' && housenumber != '' ) {
            autoComplete(form,zipcode,housenumber);
        }
    });
}

function autoComplete(form, initialZipcode = false, initialHousenumber = false) {
    let zipcode = initialZipcode.replace(/\s+/g, '');
    let housenumber = initialHousenumber.replace(/\s+/g, '');

    let url = 'https://kvxch4to30.execute-api.us-east-1.amazonaws.com/locatieserver/free?fq=postcode:'+ zipcode +'&fq=huisnummer:'+ housenumber;
    fetch( url )
        .then((response) => response.json())
        .then((json) => {
            console.log(json);

            if ( json && json.response.docs && json.response.docs[0] ) {
                form.find('[name="straatnaam"]').val(json.response.docs[0].straatnaam);
                form.find('[name="woonplaats"]').val(json.response.docs[0].woonplaatsnaam);
                form.find('[name="straatnaam"]').removeClass('border border-red-600');
                form.find('[name="woonplaats"]').removeClass('border border-red-600');
                form.find('.addressNotFound').hide();
            } else {
                form.find('[name="straatnaam"]').addClass('border border-red-600');
                form.find('[name="woonplaats"]').addClass('border border-red-600');
                form.find('.addressNotFound').show();
            }
        });
}

externalLinks = function() {
    $('a').each(function() {
        var a = new RegExp('/' + window.location.host + '/');
        if (!a.test(this.href)) {
            $(this).attr("target","_blank");
        }
    });
}
