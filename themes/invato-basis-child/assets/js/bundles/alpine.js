// Import AlpineJS
import Alpine from 'alpinejs'

// Import AlpineJS Plugins
import intersect from '@alpinejs/intersect'
import collapse from '@alpinejs/collapse'
import mask from '@alpinejs/mask'
import focus from '@alpinejs/focus'

// Import AlpineJS Data
import tabs from './../components/alpine/tabs';
// import tooltip from './../components/alpine/tooltip';
import openClose from './../components/alpine/openClose';
import searchBox from './../components/alpine/searchBox';
import radioButtonGroup from './../components/alpine/radioButtonGroup';

// Activate AlpineJS Plugins
Alpine.plugin(focus)
Alpine.plugin(intersect)
Alpine.plugin(collapse)
Alpine.plugin(mask)

// Activate AlpineJS Data
Alpine.data('openClose', openClose)
Alpine.data('tabs', tabs)
// Alpine.data('tooltip', tooltip)
Alpine.data('searchBox', searchBox)
Alpine.data('radioButtonGroup', radioButtonGroup)

// Start AlpineJS
window.Alpine = Alpine
Alpine.start()
