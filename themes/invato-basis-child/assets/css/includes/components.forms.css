/* Form fields wrapper */
.form-wrapper { @apply flex flex-wrap -mx-4; }

/* Field wrapper */
.form-field-wrapper {
    @apply mb-6 relative w-full px-4;
}
.form-field-wrapper .form-group {
    @apply mt-2;
}

/* Label */
.form-field-wrapper label.form-label {
    @apply block font-medium leading-6 text-gray-900;
}
.form-field-disabled label.field-label {
    @apply text-gray-400;
}
.form-field-wrapper label.checkbox-list-label {
    @apply text-base font-semibold leading-6 text-gray-900 mb-2;
}

/* Comment */
.form-field-wrapper .field-comment {
    @apply mt-2 text-sm text-gray-500;
}

/* Input, Textarea */
.form-field-wrapper .form-field {
    @apply relative rounded-md shadow-sm;
}
.form-field-wrapper .form-control {
    @apply block w-full rounded-md border-0 py-1.5 text-gray-900 ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-primary-600 sm:text-sm sm:leading-6 disabled:cursor-not-allowed disabled:bg-gray-50 disabled:text-gray-500 disabled:ring-gray-200;
}

/* Select/Dropdown */
.form-field-wrapper .form-select {
    @apply mt-2 block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-primary-600 sm:text-sm sm:leading-6 placeholder:text-gray-400 disabled:bg-gray-100 disabled:cursor-not-allowed disabled:border-gray-300/70;
}


/* Checkbox & Radio wrap */
.form-field-wrapper .form-check-wrap, .form-field-wrapper .form-radio-wrap {
    @apply relative flex items-start;
}
.form-field-wrapper .form-check, .form-field-wrapper .form-radio {
    @apply flex h-6 items-center;
}

/* Checkbox & Radio label */
.form-check-label-wrap {
    @apply ml-3 text-sm leading-6;
}
.form-checkbox-list-wrap {
    @apply mb-2;
}
.form-field-wrapper .form-check-label,
.form-field-wrapper .field-radio-label {
    @apply select-none font-medium text-gray-900 text-base;
}
.form-check-label-wrap .form-text {
    @apply text-gray-500;
}
.form-check-label-wrap .form-text a {
    @apply text-primary-500 font-medium hover:text-primary-600 underline;
}

.form-field-disabled .form-check-label, .form-field-disabled .form-radio-label {
    @apply cursor-not-allowed text-gray-400;
}

/* Checkbox & Radio input */
.form-check-input, .form-radio-input {
    @apply h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-600 disabled:bg-gray-100 disabled:cursor-not-allowed disabled:border-gray-400;
}

/* Radio input */
.form-field-wrapper input[type="radio"] {
    @apply rounded-full;
}

/* Colorpicker */
.form-field-color { @apply flex relative; }
.form-field-color .form-field-color-box { @apply w-10 block h-full relative; }
.form-field-color .form-field-color-box .colorpicker-bg { @apply absolute inset-0 pointer-events-none;  }
.form-field-color input[type="color"] { @apply w-10 block absolute inset-0 h-8 appearance-none border-0; }
.form-field-color .form-control { @apply rounded-l-none w-auto; }

/* Submit button */
.form-submit-button {
    @apply rounded-md bg-primary-600 py-3 px-6 font-semibold text-white shadow-sm hover:bg-primary-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary-500 transition;
}

/* Validation */
.form-field-wrapper .form-control.is-invalid {
    @apply block w-full rounded-md border-0 py-1.5 pr-10 text-red-900 ring-1 ring-inset ring-red-300 placeholder:text-red-300 focus:ring-2 focus:ring-inset focus:ring-red-500 sm:text-sm sm:leading-6;
}
.form-field-wrapper .invalid-icon { @apply hidden; }
.form-field-wrapper .form-control.is-invalid ~ .invalid-icon {
    @apply pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3;
}
.form-field-wrapper .invalid-icon-svg {
    @apply h-5 w-5 text-red-500;
}
.form-field-wrapper .invalid-feedback {
    @apply mt-1 text-sm text-red-600;
}
.form-check-input.is-invalid { @apply border-red-600 checked:border-primary-500; }

.form-field.has-error textarea {
    @apply border-red-500;
}

.form-field-error-message {
    @apply mt-2 text-sm text-red-600 font-medium;
}

/* Horizontal form */
.form-horizontal .form-field-wrapper {
    @apply grid grid-cols-4 gap-6;
}

.form-horizontal .form-field-wrapper .form-label {
    @apply col-span-1 pt-1.5;
}

.form-horizontal .form-field-wrapper .form-group {
    @apply col-span-3 mt-0;
}

.form-field-wrapper.row-1-2,
.form-field-wrapper.row-2-4 { @apply md:w-1/2; }
.form-field-wrapper.row-1-3 { @apply md:w-1/3; }
.form-field-wrapper.row-2-3 { @apply md:w-2/3; }
.form-field-wrapper.row-1-4 { @apply md:w-1/4; }
.form-field-wrapper.row-3-4 { @apply md:w-3/4; }
