.sticky_nav {
    @apply fixed top-0 inset-x-0 z-[999];
}
.hero-navbar.hero-dark .navbar {
    @apply bg-white/50;
}
.hero-navbar.is-sticky.hero-dark .navbar {
    @apply bg-white;
}
.hero-navbar.is-sticky.hero-light .navbar {
    @apply border-b;
}

.hero-wide .container { @apply xl:max-w-screen-2xl; }

.hero-navbar.hero-dark .navbar-logo.hero-logo-light { @apply block; }
.hero-navbar.hero-dark .navbar-logo.hero-logo-dark { @apply hidden; }
.hero-navbar.hero-dark.is-sticky .navbar-logo.hero-logo-light { @apply hidden; }
.hero-navbar.hero-dark.is-sticky .navbar-logo.hero-logo-dark { @apply block; }

.hero-navbar.hero-light .navbar-logo.hero-logo-light,
.hero-navbar.hero-light.is-sticky .navbar-logo.hero-logo-light { @apply hidden; }
.hero-navbar.hero-light .navbar-logo.hero-logo-dark,
.hero-navbar.hero-light.is-sticky .navbar-logo.hero-logo-dark { @apply block; }

/* Navbar menu's */
.hero-navbar.not-sticky.hero-dark .navbar .mainmenu { @apply relative z-40; }
.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul { @apply md:gap-x-8 lg:gap-x-12; }
.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li { @apply relative; }
.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li > a { @apply flex uppercase py-6 text-gray-200 hover:text-white leading-none; }
.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li.active > a { @apply text-white hover:text-gray-200 font-medium; }

.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li > ul { @apply hidden absolute top-full bg-white w-64 rounded-b shadow overflow-hidden; }
.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li:hover > ul { @apply block divide-y divide-gray-100 z-50; }
.hero-navbar.not-sticky.hero-dark .navbar .mainmenu > ul > li > ul > li > a { @apply block py-2 px-4 text-sm text-gray-500 hover:text-gray-700; }

.hero-navbar.not-sticky.hero-dark .navbar .navbar-bottom .mainmenu ul li a { @apply text-gray-500 hover:text-gray-700; }

/* Navbar buttons */
.hero-navbar.not-sticky.hero-dark .navbar .navbar-buttons a.link { @apply hidden md:inline-block uppercase leading-none text-gray-600 hover:text-gray-800; }
.hero-navbar.not-sticky.hero-dark .navbar .navbar-buttons a.button { @apply hidden md:flex py-3 px-8 rounded-md border border-transparent bg-gray-200 hover:bg-white text-gray-800 hover:text-gray-900; }
