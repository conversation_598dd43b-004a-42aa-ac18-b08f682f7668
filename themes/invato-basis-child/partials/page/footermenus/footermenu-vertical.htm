[staticMenu footerMenu]
code = "footer-menu"
==

{% set footermenuItems = footerMenu.resetMenu(this.theme.footer.site_footer_menu) %}

<ul class="menu-vertical flex flex-col items-center lg:items-start">
    <li class="menu-header">{{ footerMenu.menuName }}</li>
    {% for item in footermenuItems %}
    <li class="group {{ item.isActive or item.isChildActive ? 'active' }}">
        <a href="{{ item.url }}">{{ item.title }} {% if item.items %}<i class="fa-regular fa-sm fa-chevron-down dropdown-icon ml-1"></i>{% endif %}</a>

        {% if item.items %}
        <ul class="submenu">
            {% for subitem in item.items %}
            <li class="group {{ subitem.isActive ? 'active' }}">
                <a href="{{ subitem.url }}">{{ subitem.title }}</a>
            </li>
            {% endfor %}
        </ul>
        {% endif %}
    </li>
    {% endfor %}
</ul>