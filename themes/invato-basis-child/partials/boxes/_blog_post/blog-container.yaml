handle: blog-container
name: Blokken naast elkaar
children:
    - blog-boxes
icon: /plugins/offline/boxes/assets/img/boxes/image.svg
contexts:
- blog-boxes

form:
    tabs:
        fields:
            columns:
                label: Aantal kolommen
                type: dropdown
                options:
                    2: '2'
                    3: '3'
                    4: '4'
                    6: '6'
                default: 2
            align:
                label: Verticale uitlijning
                type: dropdown
                options:
                    'top': '<PERSON><PERSON> de bovenka<PERSON>'
                    'center': 'In het midden'
                    'bottom': 'A<PERSON> de onderkant'
                    'same_height': 'Alle blokken gelijke hoogte'
                default: 'top'
