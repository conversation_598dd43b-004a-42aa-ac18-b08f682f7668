<div class="py-6 md:py-12 lg:py-20" style="background-color: {{ box.background_color }};">
    <div class="container px-6 lg:px-8">
        <div class="mx-auto max-w-screen-xl">
            {% if box.title %}
            <div class="mb-4 lg:mb-8">
                <h2 class="text-primary-500 text-3xl md:text-4xl font-bold">{{ box.title }}</h2>
            </div>
            {% endif %}
            <div class="flex flex-col lg:grid lg:grid-cols-2 lg:gap-12">
                <img src="{{ box.image|media|resize(900, auto, { 'extension': 'webp' }) }}" alt=""
                     class="{{ box.invert == 'left' ? 'lg:order-1' : 'lg:order-2' }} rounded-3xl mb-2 lg:mb-0 shadow-os shadow-black/40">
                <div class="{{ box.invert == 'left' ? 'lg:order-2' : 'lg:order-1' }} ">
                    {% if box.subtitle %}
                    <h2 class="text-secondary-400 text-base my-2 md:my-4">{{ box.subtitle }}</h2>
                    {% endif %}
                    <div class="content_section prose-sm">
                        {{ box.content|raw }}
                    </div>
                    {% if box.buttons %}
                    <div class="flex flex-wrap items-center justify-center lg:justify-start align-items py-3 lg:py-6 space-x-0 md:space-x-6 space-y-3 md:space-y-0">
                        {% for button in box.buttons %}
                        {% partial 'ui/button' url=button.url size=button.size style=button.style icon=button.icon
                        text=button.text target_blank=button.target_blank %}
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>