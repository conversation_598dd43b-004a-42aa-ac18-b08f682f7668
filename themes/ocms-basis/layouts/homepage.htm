description = "Homepagina layout"

[bannertop]

[bannerbottom]

[popuprender]

[SiteConfig]

[searchInput]
useAutoComplete = 0
autoCompleteResultCount = 5
showProviderBadge = 0
searchPage = "zoeken.htm"

[session]
security = "all"
checkToken = 0

[authentication]
rememberMe = "ask"
twoFactorAuth = 0
recoverPassword = 1
==
<!doctype html>
<html lang="nl">
    <head>
        {% partial "site/head" %}
        {% partial "site/head-child" %}
    </head>
    <body x-data>
        {% partial "site/scripts_body_top" %}

        {% component 'bannertop' %}

        <div class="hero-navbar hero-light hero-wide" data-toggle="sticky-onscroll">
            {% partial "page/navbars/" ~ this.theme.navbar.selection|default('navbar-1') ~ ".htm" hero="true" %}
        </div>

        <main>
            {% component 'popuprender' %}
            {% page  %}
        </main>

        {% component 'bannerbottom' %}

        {% partial "page/footers/" ~ this.theme.footer.selection|default('footer-1') ~ ".htm" %}

        {% partial "page/cookie-alert" %}

        {% partial "site/foot-child" %}
        {% partial "site/foot" %}
        <script src="{{ 'assets/js/sticky.js' | theme }}"></script>
        {% partial "site/scripts_body_bottom" %}

        {% scripts %}
        {% framework extras %}
    </body>
</html>
