<div class="bg-primary-600  py-8 sm:py-16 lg:py-24">
    <div class="container px-4 text-center sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            <span class="block">{{ fields.title }}</span>
            <span class="block">{{ fields.subtitle }}</span>
        </h2>
        <div class="mt-4 text-lg leading-6 text-indigo-200">{{ fields.content|raw }}</div>
        {% if fields.buttons %}
        <div class="flex flex-wrap items-center justify-center align-items mt-6 md:mt-12 space-x-0 md:space-x-6 space-y-3 md:space-y-0">
        {% for button in fields.buttons %}
            {% partial 'ui/button' url=button.url size=button.size style=button.style icon=button.icon text=button.text target_blank=button.target_blank %}
        {% endfor %}
        </div>
        {% endif %}
    </div>
</div>
