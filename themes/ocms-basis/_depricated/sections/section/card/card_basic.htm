<div class="py-10 sm:py-20" style="background-color: {{ fields.background_color }};">
    <div class="container px-6 lg:px-8">
        <div class="mx-auto max-w-2xl lg:max-w-none">
            {% if fields.title %}
            <div class="text-center mb-6 md:mb-12">
                <h2 class="text-primary-500 text-3xl md:text-4xl font-bold">{{ fields.title }}</h2>
                {% if fields.subtitle %}
                <h2 class="text-secondary-400 text-base mt-2 md:mt-4">{{ fields.subtitle }}</h2>
                {% endif %}
            </div>
            {% endif %}
            
            <div class="group mx-auto max-w-screen-xl">
                <div class="grid md:grid-cols-2 lg:grid-cols-3">
                    {% for key,item in fields.card %}

                    {% if key == '0' %}
                    {% set borderSettings = 'border-b md:border-r border-primary-500 lg:border-secondary-100 lg:hover:border-primary-500' %}
                    {% elseif key == '1' %}
                    {% set borderSettings = 'border-b lg:border-x border-primary-500 lg:border-secondary-100 lg:hover:border-primary-500' %}
                    {% elseif key == '2' %}
                    {% set borderSettings = 'border-b md:border-r lg:border-r-0 lg:border-l border-primary-500 lg:border-secondary-100 lg:hover:border-primary-500' %}
                    {% elseif key == '3' %}
                    {% set borderSettings = 'border-b lg:border-b-0 lg:border-t lg:border-r border-primary-500 lg:border-secondary-100 lg:hover:border-primary-500' %}
                    {% elseif key == '4' %}
                    {% set borderSettings = 'border-b md:border-b-0 md:border-r lg:border-t lg:border-x border-primary-500 lg:border-secondary-100 lg:hover:border-primary-500' %}
                    {% elseif key == '5' %}
                    {% set borderSettings = 'lg:border-t lg:border-l border-primary-500 lg:border-secondary-100 lg:hover:border-primary-500' %}
                    {% else %}
                    {% set borderSettings = '' %}
                    {% endif %}

                        <div class="{{ borderSettings }} p-2 md:p-4 lg:p-6">
                            <div class="flex flex-col items-center justify-center text-center space-y-2 md:space-y-4">
                                <p class="text-base uppercase text-primary-500 lg:text-secondary-500 font-bold">{{ item.title }}</p>
                                <p class="text-sm text-secondary-400 leading-relaxed">{{ item.content }}</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>
