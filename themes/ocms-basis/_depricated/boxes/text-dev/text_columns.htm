<div class="{{ box.style == 'light' ? 'bg-white' : 'bg-primary-600' }} py-10 sm:py-20">
    <div class="container px-6 lg:px-8">
        <h2 class="{{ box.style == 'light' ? 'text-primary-600' : 'text-white' }}">
            <span class="text-3xl md:text-5xl font-bold">{{ box.title }}</span>
            <span class="text-primary-300 text-2xl tracking-wider ml-3">{{ box.subtitle }}</span>
        </h2>

        {% if box.content|length == 2 %}
        {% set column = 'grid grid-cols-2 gap-24' %}
        {% elseif box.content|length == 3 %}
        {% set column = 'grid grid-cols-3 gap-20' %}
        {% else %}
        {% set column = 'flex items-center justify-center' %}
        {% endif %}

        <div class="{{ column }} mt-12">
            {% for item in box.content %}
            <div class="flex flex-col space-y-8 {{ box.style == 'light' ? 'text-primary-600' : 'text-white' }}">
                <h2 class="border-b-2 text-3xl border-primary-300 pb-0.5">{{ item.title }}</h2>
                <div class="prose {{ box.style == 'light' ? 'text-primary-600' : 'text-white' }}">{{ item.content|content }}</div>
                {% if item.slugtext %}
                <div class="inline-flex items-center">
                    <a href="" class="{{ box.style == 'light' ? 'hover:text-primary-400' : 'hover:text-primary-200' }} uppercase tracking-widest">{{ item.slugtext }} <i class="fa-duotone fa-chevrons-right ml-1"></i></a>
                </div>
                {% endif %}
            </div>
            {% endfor %}
        </div>
    </div>
</div>