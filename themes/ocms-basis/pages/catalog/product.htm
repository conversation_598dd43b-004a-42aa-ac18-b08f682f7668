url = "/producten/:slug"
layout = "default"
title = "Product"

[ProductDetail]
slug = "{{ :slug }}"
==
<?php

    function onEnd()
    {
        $post = $this->components['ProductDetail'];
        $postTitle = $post->product?->title;
        $postMetaDesc = $post->product?->meta_description;
        $postOgDesc = $post->product?->og_description;

        if (
            isset($post->product?->meta_title)
            &&
            trim($post->product?->meta_title) !== ''
        ) {
            $seoTitle = $post->product?->meta_title;
        }
        $seoDesc = '';
        if (
            isset($postOgDesc)
            &&
            trim($postOgDesc) !== ''
        ) {
            $seoDesc = $postOgDesc;
        }
        if (
            isset($postMetaDesc)
            &&
            trim($postMetaDesc) !== ''
        ) {
            $seoDesc = $postMetaDesc;
        }

        $title = $seoTitle ?? $postTitle;
        $this->page->title = $title;
        $this->page->meta_description = $seoDesc;
    }

?>
==
<div class="bg-white">
<div class="py-2 border-t shadow bg-gray-100 font-sans">
    <div class="container">
        <nav class="flex" aria-label="Breadcrumb">
            <ol role="list" class="flex items-center space-x-4">
                {# Index #}
                <li>
                    <div class="flex items-center">
                        <a href="/" class="text-sm font-medium hover:text-gray-800" style="color: #16588d;"><i class="fa-solid fa-house"></i></a>
                    </div>
                </li>

                {# Overview #}
                {% if catalogPage %}
                {% set resolvedCatalog = link(catalogPage) %}
                <li>
                    <div class="flex items-center">
                        <svg class="h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                        </svg>

                        <a href="{{ catalogPage|link }}" class="ml-4 text-sm font-medium hover:text-gray-800" style="color: #16588d;">{{ resolvedCatalog.title }}</a>
                    </div>
                </li>
                {% endif %}

                {# Category #}
                {% if product.categories|length > 0 %}
                {% set category = product.categories|first %}
                <li>
                    <div class="flex items-center">
                        <svg class="h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                        </svg>

                        <a href="{{ categoryPage | page({ slug: category.slug }) }}" class="ml-4 text-sm font-medium hover:text-gray-800" style="color: #16588d;">{{ category.title }}</a>
                    </div>
                </li>
                {% endif %}

                {# Product #}
                <li>
                    <div class="flex items-center">
                        <svg class="h-5 w-5 flex-shrink-0 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
                        </svg>

                        <span class="ml-4 text-sm font-medium hover:text-gray-800" style="color: #525252;">{{ product.title }}</span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>
</div>
  <div class="mx-auto py-8 md:py-16 px-4 sm:px-6 lg:max-w-7xl lg:px-8">
    <!-- Product -->
    <div class="lg:grid lg:grid-cols-7 lg:grid-rows-1 lg:gap-x-8 lg:gap-y-10 xl:gap-x-16">
      <!-- Product image -->
      <div class="lg:col-span-4 lg:row-end-1">
        {% partial 'atomic/molecules/catalog/product-slider' product = product %}
      </div>

      <!-- Product details -->
      {% partial 'atomic/molecules/catalog/details' product = product %}

      <!-- Tabs -->
      {% partial 'atomic/molecules/catalog/tabs' product = product %}

    </div>
  </div>
</div>
{# Add SEO meta tags to page #}
{% set seo_object = product %}
{% partial 'site/seo_meta' item = seo_object %}
