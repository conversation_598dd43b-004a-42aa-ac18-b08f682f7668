<footer id="footer-7">
    <div class="footer">
        <div class="container">
            <div class="footer-row flex flex-col lg:flex-row items-center justify-between">
                <nav class="footermenu md:order-2 lg:order-1">
                    {% partial 'page/footermenus/footermenu' %}
                </nav>

                {% if this.theme.footer.buttons.btn %}
                <div class="buttons flex flex-col md:flex-row items-center md:order-1 lg:order-2">

                    {% for button in this.theme.footer.buttons.btn %}
                        <div class="">
                            {% partial 'ui/button' url=button.url text=button.text size=button.size style=button.style
                            target_blank=button.target_blank icon=button.icon %}
                        </div>
                    {% endfor %}

                </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="footer-bottom">
        <div class="container">
            <div class="footer-row flex flex-wrap flex-col md:flex-row items-center md:justify-between">
                <div class="legal legal-light">
                    {% partial 'ui/footer/legal' %}
                </div>
                <div class="social-media hidden lg:block">
                    {% partial 'ui/footer/social-media' %}
                </div>
                <div class="copyright copyright-light">
                    {% partial 'ui/footer/copyright' %}
                </div>
            </div>
        </div>
    </div>
</footer>
