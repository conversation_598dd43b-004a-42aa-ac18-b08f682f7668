{% set item = notificationrender.notification %}
{% set current_date = date() %}
{% set start_date = date(item.start_date) %}
{% set end_date = date(item.end_date) %}
{% set inactive = item.inactive %}
{% set button = item.button %}

{% set textcolor = "text-white" %}

{% if item.style == "primary" %}
{% set maincolor = "bg-primary-500" %}
{% set darkcolor = "bg-primary-600" %}
{% set iconType = "circle-check" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "secondary" %}
{% set maincolor = "bg-secondary-500" %}
{% set darkcolor = "bg-secondary-600" %}
{% set iconType = "circle-check" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "success" %}
{% set maincolor = "bg-green-500" %}
{% set darkcolor = "bg-green-600" %}
{% set iconType = "circle-check" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "warning" %}
{% set maincolor = "bg-amber-500" %}
{% set darkcolor = "bg-amber-600" %}
{% set iconType = "circle-exclamation" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "danger" %}
{% set maincolor = "bg-red-500" %}
{% set darkcolor = "bg-red-600" %}
{% set iconType = "circle-xmark" %}
{% set textcolor = "text-white" %}

{% elseif item.style == "info" %}
{% set maincolor = "bg-sky-500" %}
{% set darkcolor = "bg-sky-600" %}
{% set iconType = "circle-info" %}
{% set textcolor = "text-white" %}

{% endif %}

{% if current_date > start_date and current_date < end_date %}
<div id="notifications" class="notifications">
    <div class="container">
        <div class="{{ maincolor }} {{ inactive ? 'hidden' }} rounded-lg shadow-2xl">
            <div class="p-3 md:p-6">
                <div class="flex items-start w-full">
                        <span class="hidden md:flex rounded-lg {{ darkcolor }} p-2">
                            <i class="fa-regular fa-{{ iconType }} {{ textcolor }} w-10 h-10"></i>
                        </span>
                    <div class="font-medium md:px-4 xl:px-8 w-full">
                        {% if item.title %}
                        <div class="{{ textcolor }} uppercase text-base md:text-lg font-bold mb-4">{{ item.title }}
                        </div>
                        {% endif %}
                        <div class="flex flex-col space-y-4 md:space-y-0 md:flex-row md:items-center md:justify-between md:space-x-4 xl:space-x-8">
                            <div class="{{ textcolor }}">{{ item.notification|content }}</div>

                            {% if button %}
                            <div class="pt-4 md:pt-0">
                                {% for button in button %}
                                <div class="">
                                    {% partial 'ui/button' item = button %}
                                </div>
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
