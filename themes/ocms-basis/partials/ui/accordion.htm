<div x-data="{ activeTab: '' }">

    <dl class="space-y-4 divide-y divide-gray-200">

        <div class="pt-4" x-id="['accordion']">
        <dt class="text-lg">
            <button
              type="button"
              class="text-left w-full flex justify-between items-start text-gray-400"
              :aria-controls="$id('accordion')"
              :aria-expanded="activeTab === $id('accordion') ? 'true' : 'false'"
              @click="activeTab !== $id('accordion') ? activeTab = $id('accordion') : activeTab = null"
            >
            <span class="font-medium text-gray-900"> What&#039;s the best thing about Switzerland? </span>
            <span class="ml-6 h-7 flex items-center">
                <svg
                  class="rotate-0 h-6 w-6 transform"
                  :class="{ '-rotate-180': activeTab === $id('accordion'), 'rotate-0': activeTab != $id('accordion') }"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
            </span>
            </button>
        </dt>
        <dd
            class="mt-2 pr-12"
            :id="$id('accordion')"
            x-show="activeTab === $id('accordion')"
            x-collapse
        >
            <p class="text-base text-gray-500">I don&#039;t know, but the flag is a big plus. Lorem ipsum dolor sit amet consectetur adipisicing elit. Quas cupiditate laboriosam fugiat.</p>
        </dd>
        </div>

        <div class="pt-4" x-id="['accordion']">
        <dt class="text-lg">
            <button
              type="button"
              class="text-left w-full flex justify-between items-start text-gray-400"
              :aria-controls="$id('accordion')"
              :aria-expanded="activeTab === $id('accordion') ? 'true' : 'false'"
              @click="activeTab !== $id('accordion') ? activeTab = $id('accordion') : activeTab = null"
            >
            <span class="font-medium text-gray-900"> How do you make holy water? </span>
            <span class="ml-6 h-7 flex items-center">
                <svg
                  class="rotate-0 h-6 w-6 transform"
                  :class="{ '-rotate-180': activeTab === $id('accordion'), 'rotate-0': activeTab != $id('accordion') }"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
            </span>
            </button>
        </dt>
        <dd
            class="mt-2 pr-12"
            :id="$id('accordion')"
            x-show="activeTab === $id('accordion')"
            x-collapse
        >
            <p class="text-base text-gray-500">You boil the hell out of it. Lorem ipsum dolor sit amet consectetur adipisicing elit. Magnam aut tempora vitae odio inventore fuga aliquam nostrum quod porro. Delectus quia facere id sequi expedita natus.</p>
        </dd>
        </div>

        <div class="pt-4" x-id="['accordion']">
        <dt class="text-lg">
            <button
              type="button"
              class="text-left w-full flex justify-between items-start text-gray-400"
              :aria-controls="$id('accordion')"
              :aria-expanded="activeTab === $id('accordion') ? 'true' : 'false'"
              @click="activeTab !== $id('accordion') ? activeTab = $id('accordion') : activeTab = null"
            >
            <span class="font-medium text-gray-900"> What do you call someone with no body and no nose? </span>
            <span class="ml-6 h-7 flex items-center">
                <svg
                  class="rotate-0 h-6 w-6 transform"
                  :class="{ '-rotate-180': activeTab === $id('accordion'), 'rotate-0': activeTab != $id('accordion') }"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  aria-hidden="true"
                >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                </svg>
            </span>
            </button>
        </dt>
        <dd
            class="mt-2 pr-12"
            :id="$id('accordion')"
            x-show="activeTab === $id('accordion')"
            x-collapse
        >
            <p class="text-base text-gray-500">Nobody knows. Lorem ipsum dolor sit amet consectetur adipisicing elit. Culpa, voluptas ipsa quia excepturi, quibusdam natus exercitationem sapiente tempore labore voluptatem.</p>
        </dd>
        </div>
        

        <!-- More questions... -->
    </dl>
</div>