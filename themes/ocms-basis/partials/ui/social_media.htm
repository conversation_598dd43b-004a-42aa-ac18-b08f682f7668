{% if socials %}
{% set textColor = 'text-gray-400' %}
{% if text_color %}
    {% set textColor = text_color %}
{% endif %}
<div class="{{ no_margin ? '' : 'mt-9' }}">
    <ul class="flex flex-wrap md:flex-nowrap gap-x-4 md:gap-x-4">
        {% for item in socials %}

        {% if item.title == 'facebook' %}
        {% set brandcolor = "hover:text-social-facebook" %}
        {% elseif item.title == 'instagram' %}
        {% set brandcolor = "hover:text-social-instagram" %}
        {% elseif item.title == 'snapchat' %}
        {% set brandcolor = "hover:text-social-snapchat" %}
        {% elseif item.title == 'tiktok' %}
        {% set brandcolor = "hover:text-social-tiktok" %}
        {% elseif item.title == 'youtube' %}
        {% set brandcolor = "hover:text-social-youtube" %}
        {% elseif item.title == 'linkedin' %}
        {% set brandcolor = "hover:text-social-linkedin" %}
        {% elseif item.title == 'github' %}
        {% set brandcolor = "hover:text-social-github" %}
        {% else %}
        {% set brandcolor = "hover:text-white" %}
        {% endif %}

        <li class="">
            <a href="{{ item.slug }}" target="_blank" class="group transition">
                <span class="sr-only">{{ item.title }}</span>
                <i class="transition fab fa-{{ item.title }} text-2xl {{ textColor }} {{ brandcolor }}"></i>
            </a>
        </li>
        {% endfor %}
    </ul>
</div>

{% elseif this.theme.social.media %}

<div class="mt-9">
    <ul class="flex flex-wrap md:flex-nowrap gap-x-4 md:gap-x-4">
        {% for item in this.theme.social.media %}

        {% if item.title == 'facebook' %}
        {% set brandcolor = "hover:text-social-facebook text-gray-400" %}
        {% elseif item.title == 'instagram' %}
        {% set brandcolor = "hover:text-social-instagram text-gray-400" %}
        {% elseif item.title == 'snapchat' %}
        {% set brandcolor = "hover:text-social-snapchat text-gray-400" %}
        {% elseif item.title == 'tiktok' %}
        {% set brandcolor = "hover:text-social-tiktok text-gray-400" %}
        {% elseif item.title == 'youtube' %}
        {% set brandcolor = "hover:text-social-youtube text-gray-400" %}
        {% elseif item.title == 'linkedin' %}
        {% set brandcolor = "hover:text-social-linkedin text-gray-400" %}
        {% elseif item.title == 'github' %}
        {% set brandcolor = "hover:text-social-github text-gray-400" %}
        {% else %}
        {% set brandcolor = "hover:text-white text-gray-400" %}
        {% endif %}

        <li class="">
            <a href="{{ item.slug }}" target="_blank" class="group transition">
                <span class="sr-only">{{ item.title }}</span>
                <i class="transition fab fa-{{ item.title }} text-2xl {{ brandcolor }}"></i>
            </a>
        </li>
        {% endfor %}
    </ul>
</div>

{% endif %}
