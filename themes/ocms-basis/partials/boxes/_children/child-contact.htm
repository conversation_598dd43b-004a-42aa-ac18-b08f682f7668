{% set padding = '' %}
{% if box.padding == 'sm' %}
    {% set padding = 'p-2' %}
{% elseif box.padding == 'md' %}
    {% set padding = 'p-4' %}
{% elseif box.padding == 'lg' %}
    {% set padding = 'p-5' %}
{% endif %}

<div class="{{ box.text_color == 'dark' ? 'light' : 'dark' }} overflow-hidden relative {{ box.rounded_corners ? 'rounded-md' }} {{ box.shadow ? 'shadow-lg' }}" {% if box.corner %}style="border: {{ box.corner_width }}px solid {{ box.corner_color }};"{% endif %}>
    <div
        class="absolute inset-0"
        style="background-color: {{ box.background_color|default('#ffffff') }}; {% if box.bg_opacity != '100' %}opacity: .{{ box.bg_opacity }};{% endif %}"
    ></div>

    <div class="relative {{ padding }}">
        <h2 class="text-3xl font-bold tracking-tight {{ box.text_color == 'light' ? 'text-white' }}">{{ box.title }}</h2>
        <div class="content_section mt-6">
            <p>{{ box.intro_text }}</p>
        </div>

        {% if company %}
            {% partial 'ui/contact_info' text_color = box.text_color %}
        {% else %}
            {% partial 'ui/_legacy/contact_info' %}
        {% endif %}

        <div class="mt-6 text-lg leading-8 text-gray-600">{{ box.text | content }}</div>
    </div>
</div>
