handle: child-pros-v2
name: Voordelen
section: Sub-blokken
icon: /plugins/offline/boxes/assets/img/boxes/image.svg
contexts:
- child-boxes

form:
    tabs:
        fields:
            text:
                label: Tekst
                type: richeditor
                size: huge
                tab: Algemeen
            pros:
                type: repeater
                prompt: Voordeel toevoegen
                displayMode: accordion
                titleFrom: label
                itemsExpanded: false
                tab: Algemeen
                form:
                    fields:
                        label:
                            label: Titel
                            placeholder: 'producten'
            buttons:
                type: mixin
                tab: Algemeen
            _colors:
                label: Kleuren
                type: section
                tab: Design
            background_color:
                label: Achtergrond kleur
                type: colorpicker
                availableColors: [ "transparent","#ffffff","#f3f4f6","#d1d5db","#6b7280", "#374151", "#18181b" ]
                default: "transparent"
                allowCustom: true
                allowEmpty: true
                tab: Design
            bg_opacity:
                label: Achtergrond transparantie
                comment: "0 is doorzichtig, 100 is ondoorzichtig"
                type: number
                default: 100
                min: 0
                max: 100
                steps: 10
                span: left
                tab: Design
            text_color:
                mixin: text-color
                type: mixin
                tab: Design
            _effects:
                label: Overig
                type: section
                tab: Design
            rounded_corners:
                label: Afgeronde hoeken
                type: switch
                tab: Design
            shadow:
                label: Schaduw
                type: switch
                tab: Design
            corner:
                label: Randen
                type: switch
                tab: Design
            corner_width:
                label: Rand dikte
                type: dropdown
                options:
                    1: 1 pixel
                    2: 2 pixels
                    3: 3 pixels
                default: 1
                trigger:
                    action: show
                    field: corner
                    condition: checked
                tab: Design
            corner_color:
                label: Rand kleur
                type: colorpicker
                availableColors: [ "transparent","#ffffff","#f3f4f6","#d1d5db","#6b7280", "#374151", "#18181b" ]
                default: "transparent"
                allowCustom: true
                allowEmpty: true
                trigger:
                    action: show
                    field: corner
                    condition: checked
                tab: Design
            child-col-span:
                type: mixin
                tab: Indeling
