<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="cta-2"
    data-category="call-to-actions"
    class="relative {{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container data-rel="boxes-wrapper">

    {% if box.bg_image %}
        <div class="absolute inset-0 pointer-events-none z-10">
            {% if box.bg_overlay_color %}
                <div
                    class="absolute inset-0 z-30"
                    style="background-color: {{ box.bg_overlay_color }}; opacity: .{{ box.bg_overlay_alpha }};">
                </div>
            {% endif %}
            {% if box.bg_image %}
                {% partial 'atomic/atoms/media/image' img=box.bg_image resize_w="1920" class="w-full h-full object-cover relative z-20" %}
            {% endif %}
        </div>
    {% endif %}

    <div class="container relative z-30">
        <div class="max-w-4xl mx-auto">
            {% if box.title or box.subtitle or box.content %}
                {% partial 'atomic/molecules/content-heading-centered' box=box truncate=300 %}
            {% endif %}
            {% if box.buttons %}
                {% partial 'atomic/molecules/buttons' buttons=box.buttons class="buttons-wrap justify-center" %}
            {% endif %}
        </div>
    </div>
</section>
