<section
    {% if box.anchor %}id="{{ box.anchor }}"{% endif %}
    data-name="team-4"
    data-category="team"
    class="{{ box.custom_css }} {{ box.is_dark_bg ? 'dark' }}"
    style="background-color: {{ box.background_color }};"
    data-boxes-container
    data-rel="boxes-wrapper">

	<div class="container space-y-8 xl:space-y-12">

        <div class="">
            {% if box.title or box.subtitle or box.content %}
                {% partial 'atomic/molecules/content-heading-centered' box=box %}
            {% endif %}

            {% if box.buttons %}
                {% partial 'atomic/molecules/buttons' buttons=box.buttons class="flex justify-center space-x-4" %}
            {% endif %}
        </div>

        {% if box.department %}
            {% partial 'site/dynamic-department' department=box.department pagination=box.pagination itemsPerPage=box.itemsPerPage team_box="4" %}
        {% else %}
            {% partial 'atomic/organisms/team/tabs' pagination=box.pagination itemsPerPage=box.itemsPerPage team_box="4"  %}
        {% endif %}
    </div>

</section>