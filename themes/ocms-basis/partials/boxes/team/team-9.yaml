handle: team-9
name: Team 9
section: Team
icon: /app/assets/boxes/teams/team-9.png
order: 190

spacing:
    - general

form:
    tabs:
        fields:
            mixin-title-subtitle:
                type: mixin
                tab: Algemeen
            content:
                type: mixin
                tab: Algemeen
            buttons:
                type: mixin
                tab: Algemeen
            department:
                label: Afdeling filter
                type: dropdown
                emptyOption: '-- geen afdeling filter --'
                optionsMethod: listAllDepartments
                tab: Algemeen
            background_color:
                type: mixin
                tab: Design
            pagination:
                label: Paginatie
                type: switch
                tab: Design
            itemsPerPage:
                label: Items per pagina
                type: dropdown
                placeholder: Kies een aantal
                options:
                    4: 4
                    8: 8
                    12: 12
                    16: 16
                tab: Design
                trigger:
                    action: show
                    field: pagination
                    condition: checked
            anchor:
                type: mixin
                tab: Design
            custom_css:
                type: mixin
                tab: Design
