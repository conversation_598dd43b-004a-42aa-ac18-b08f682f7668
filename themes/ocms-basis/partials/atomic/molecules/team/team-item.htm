{% set memberName = item.name ~ " " ~ item.surname %}
<div class="space-y-4 px-4 md:px-8 xl:px-12 py-8">
    <div class="">
        {% partial 'atomic/atoms/media/image' img=item.img resize_w='320' class='w-32 h-32 rounded-full mx-auto object-cover' %}
    </div>

    <div class="flex flex-col items-center justify-center text-center ">

        <div class="items-center prose prose-primary dark:prose-primary_inverted max-w-none">
            {% partial 'atomic/atoms/headings/header-span' text=memberName level=3 headingClass='member-name' %}
            {% if item.title %}
                {% partial 'atomic/atoms/headings/header-span' text=item.title level=5 headingClass='member-title' %}
            {% endif %}
        </div>

        <div class="flex flex-1 flex-col gap-4">
            {% if item.email or item.phonenumber %}
            <div class="flex flex-col prose prose-a:no-underline hover:prose-a:underline prose-primary dark:prose-primary_inverted max-w-none">
                {% if item.email %}
                    <a href="mailto:{{ html_email(item.email)|raw }}" title="" itemprop="email"
                       class="">{{ html_email(item.email)|raw }}</a>
                {% endif %}
                {% if item.phonenumber %}
                    <a href="tel:{{ item.phonenumber }}" title="" itemprop="telephone"
                       class="">{{ item.phonenumber }}</a>
                {% endif %}
            </div>
            {% endif %}

            {% if item.description %}
            <div class="text-center prose-p:text-gray-900 prose prose-sm prose-primary dark:prose-p:text-gray-300 dark:prose-primary_inverted max-w-none">
                {% partial 'atomic/molecules/content-section' content=item.description %}
            </div>
            {% endif %}

            {% if item.socials %}
                <div class="flex justify-center">
                    <ul class="flex items-center justify-center flex-wrap gap-x-4 gap-y-2">
                        {% for item in item.socials %}
                            {% partial 'atomic/atoms/team/socials' item=item %}
                        {% endfor %}
                    </ul>
                </div>
            {% endif %}
        </div>

    </div>
    
</div>