<div class="relative group/card flex items-center justify-center bg-primary-200 dark:bg-primary-500 hover:bg-primary-300 dark:hover:bg-primary-600 transition-all rounded-lg overflow-hidden py-6 px-16">
    <div class="absolute inset-0">
        {% partial 'atomic/atoms/media/image' class='w-full h-full object-cover group-hover/card:scale-110 transition-all duration-300'
            img=item.image title=item.img_title resize_w='330' %}
    </div>
    <div class="relative text-center bg-white group-hover/card:bg-primary-50 text-primary-900 dark:bg-gray-600 dark:group-hover/card:bg-gray-700 dark:text-gray-200 font-bold tracking-wide text-sm rounded-md shadow-sm leading-none px-6 py-3">
        {{ item.title }}
    </div>
    <a href="{{ categoryPage | page({ slug: item.slug }) }}" alt="{{ item.title }}" class="absolute inset-0"></a>
</div>