[PortfolioCategoryList]
==
{% set categories = PortfolioCategoryList.categories %}

<div class="col-span-4 pt-12 lg:pt-0 flex flex-col gap-8 md:w-2/3 md:mx-auto lg:gap-4 lg:w-full xl:gap-8">

    <div class="bg-white dark:bg-gray-500 md:rounded-lg p-4 -mx-4 md:mx-0 md:p-8 lg:p-4 xl:p-8 order-1 lg:order-3">
        <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
            {% set sidebarLabel = 'Overige artikelen:'|_ %}
            {% partial 'atomic/atoms/cards/card-heading' text=sidebarLabel level="4" class="font-bold" %}
        </div>
        <div class="divide-y divide-gray-400 dark:divide-gray-300">
            {% set projects = PortfolioProjectList.projects.sortByDesc('id').where('id', '!=', project.id).take(3) %}
            {% for item in projects %}
                {% if item.slug != slug %}

                    {% partial 'atomic/molecules/portfolio/portfolio-item-card-xs' item=item class=' py-4' %}

                {% endif %}
            {% endfor %}
        </div>
    </div>

    <div class="flex flex-col gap-8 lg:gap-4 xl:gap-8 order-last">

        {% if categories.toArray() %}
            <div class="bg-white dark:bg-gray-500 md:rounded-lg p-4 -mx-4 md:mx-0 md:p-8 lg:p-4 xl:p-8">
                <div class="space-y-4">
                    <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                        {% set sidebarLabel = 'Categorieën:'|_ %}
                        {% partial 'atomic/atoms/cards/card-heading' text=sidebarLabel level="4" class="font-bold" %}
                    </div>
                    <div class="space-y-4">
                        {% for item in categories %}
                            {% partial 'atomic/molecules/portfolio/portfolio-category-card' item=item %}
                        {% endfor %}
                    </div>
                </div>
            </div>
        {% endif %}

        {% if socials %}
            <div class="bg-white dark:bg-gray-500 md:rounded-lg p-4 -mx-4 md:mx-0 md:p-8 lg:p-4 xl:p-8">
                <div class="space-y-8">

                    <div class="space-y-4">
                        <div class="prose prose-primary dark:prose-primary_inverted max-w-none">
                            {% set sidebarLabel = 'Volg ons op:'|_ %}
                            {% partial 'atomic/atoms/cards/card-heading' text=sidebarLabel level="4" class="font-bold" %}
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            {% for item in socials %}
                                {% partial 'atomic/atoms/blog/blog-socials' item=item %}
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>

</div>
