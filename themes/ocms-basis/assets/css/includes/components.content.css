.content .content-title h2 { @apply text-gray-600; }

.content .media-row { @apply md:space-y-4 lg:space-y-0; }

.content .content-usps { @apply gap-x-8 mt-8; }
.content .content-usps .content-usp { @apply flex gap-x-4 lg:gap-x-0 lg:block mb-4 lg:mb-0; }
.content .content-usps .content-usp .usp-icon { @apply text-2xl lg:mb-2; }
.content .content-usps .content-usp .usp-title h4 { @apply text-base text-gray-600 font-normal my-0; }
.content .content-usps .content-usp .usp-text { @apply col-span-3 text-sm text-gray-400; }

#content-1 .content-title { @apply mb-8 sm:mb-12 lg:mb-16; }
#content-1 .content-title h2 { @apply text-center; }
#content-1 .card { @apply bg-gray-100 text-center; }
#content-1 .content-row { @apply max-w-5xl mx-auto px-8 flex flex-col lg:flex-row gap-y-8 lg:gap-y-0 lg:gap-x-8; }
#content-1 .content-row .card { @apply flex-1; }

#content-2.content { @apply py-0 md:p-4 lg:p-0; }
#content-2 .card { @apply max-w-sm text-center; }

#content-3 .row-grid { @apply gap-x-16 items-center sm:items-start lg:items-center; }
/* #content-3.style-1 .row-grid,
#content-3.style-3 .row-grid { @apply gap-x-16; }
#content-3 .row-grid,
#content-3.style-2 .row-grid { @apply gap-x-16 items-center sm:items-start lg:items-center; }
#content-3.style-3 .content-grid-text { @apply py-16; } */
