/* Slider Boxes */
.slider-wrap .slider-intro { @apply mt-6 max-w-xl mx-auto px-4; }

/* .slider-wrap .slick-dots { @apply bg-primary-highlight dark:bg-gray-800 flex justify-center items-center gap-2 rounded-full w-auto; } */
/* .slider-wrap .slick-dots li { @apply text-primary; } */
/* .slider-wrap .slick-dots button { @apply w-6 md:w-12 h-2 rounded-full text-[0px] leading-[0] text-transparent; }
.slider-wrap .slick-dots .slick-active button { @apply bg-primary dark:bg-primary-darker; }
.slider-slider-control { @apply text-2xl text-primary hover:text-primary-hover transition-all cursor-pointer dark:text-primary-darker dark:hover:text-primary select-none; } */

.slider-6-wrap .slider-slider-control-wrap { @apply from-gray-800 hover:from-gray-800 to-transparent text-white opacity-50 hover:opacity-100 transition; }
.slider-6-wrap .slider-6-slider-prev-wrap { @apply bg-gradient-to-r; }
.slider-6-wrap .slider-6-slider-next-wrap { @apply bg-gradient-to-l; }
.slider-6-wrap .slider-slider-control { @apply text-5xl text-white/70 hover:text-white transition-all cursor-pointer; }
.slider-6-wrap .slick-dots { @apply bg-gray-200 dark:bg-gray-800; }
.slider-6-wrap .slick-dots .slick-active button { @apply bg-primary dark:bg-primary-darker; }

.slider-8-wrap .slider-slider-control-wrap { @apply from-gray-800 hover:from-gray-800 to-transparent text-white opacity-50 hover:opacity-100 transition; }
.slider-8-wrap .slider-8-slider-prev-wrap { @apply bg-gradient-to-r; }
.slider-8-wrap .slider-8-slider-next-wrap { @apply bg-gradient-to-l; }
.slider-8-wrap .slider-slider-control { @apply text-5xl text-white/70 hover:text-white transition-all cursor-pointer select-none; }
.slider-8-wrap .slick-dots { @apply bg-gray-200; }
.slider-8-wrap .slick-dots .slick-active button { @apply bg-gray-500; }
