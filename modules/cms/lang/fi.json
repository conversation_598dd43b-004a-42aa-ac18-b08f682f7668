{"Themes": "<PERSON><PERSON><PERSON>", "Manage Themes": "<PERSON>its<PERSON> tee<PERSON>ja", "Customize Theme": "<PERSON><PERSON><PERSON><PERSON> teema", "Manage Maintenance Mode": "Hallitse ylläpitotilaa", "View Website During Maintenance": "Tarkastele verkkosivustoa huo<PERSON> a<PERSON>na", "Manage Content": "Hallitse sisältöä", "Manage Asset Files": "Hallitse omaisuustiedostoja", "Manage Pages": "Hallitse sivu<PERSON>", "Manage Layouts": "<PERSON><PERSON><PERSON>", "Configure the maintenance mode page and toggle the setting.": "<PERSON><PERSON><PERSON><PERSON> huoltotilan sivua ja valitse asetus.", "Maintenance Mode": "Ylläpitotila", "Select the page to show when maintenance mode is activated.": "Valitse sivu näytettäväksi kun huoltotila on käytössä.", "Enable maintenance mode": "<PERSON>ta huoltotila k<PERSON>yttöön", "Maintenance mode will display the maintenance page to visitors who are not signed in to the back-end area.": "Huoltotila nä<PERSON>tää huoltosivun vierailijoille, jotka eivät ole kirjautuneena hallintapa<PERSON>.", "Resources": "Resurssit", "Reference assets and variables included on this page.": "Tällä sivulla olevat viitemateriaalit ja muuttujat.", "JavaScript": "JavaScript", "JavaScript file(s) in the assets/js folder": "JavaScript-tiedosto(t) sisältö/js-kansiossa", "LESS": "VÄHEMMÄN", "LESS file(s) in the assets/less folder": "VÄHEMMÄN tiedosto(t) resurssit/vähemmän-kansiossa", "SCSS": "SCSS", "SCSS file(s) in the assets/scss folder": "SCSS-tiedosto(t) resurssit/scss-kansiossa", "Variables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Page variables name(s) and value(s)": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nimet ja arvot", "Headers": "Otsikot", "Page header name(s) and value(s)": "<PERSON><PERSON>n otsikon nimet ja arvot", "Site Picker": "<PERSON><PERSON><PERSON> valitsin", "Displays links for selecting a different site.": "Näyttää linkit toisen sivuston valitsemiseksi.", "Unknown component": "<PERSON><PERSON><PERSON><PERSON> kompo<PERSON>", "View Bag": "Näytä kori", "Stores custom template properties.": "Tallentaa mukautetun mallin omina<PERSON>t.", "By :name": "Tekijä: :name", "The theme ':name' is not found.": "<PERSON><PERSON><PERSON> ':name' ei <PERSON>.", "Manage the front-end theme and customization options.": "<PERSON><PERSON><PERSON> sivuston teemaa ja muokka<PERSON>.", "Frontend Theme": "<PERSON><PERSON><PERSON> teema", "Properties": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Publish": "Julkai<PERSON>", "Advanced": "<PERSON><PERSON><PERSON><PERSON>", "Name": "<PERSON><PERSON>", "New theme name": "<PERSON><PERSON><PERSON> teeman nimi", "Author Name": "Julkaisija", "Person or company name": "<PERSON><PERSON><PERSON><PERSON><PERSON> tai y<PERSON>ksen nimi", "Directory Name": "<PERSON><PERSON><PERSON><PERSON> nimi", "The destination theme directory": "<PERSON><PERSON> k<PERSON>", "Description": "<PERSON><PERSON><PERSON>", "Theme description": "<PERSON><PERSON> k<PERSON>", "Homepage": "Ko<PERSON>iv<PERSON>", "Website URL": "Verkkosivujen URL-osoite", "Author Code": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "A unique code for the author used for distribution": "Uniikki j<PERSON><PERSON><PERSON><PERSON> k<PERSON>, jota käytetään j<PERSON>", "Theme Code": "<PERSON><PERSON> koodi", "A unique code for this theme used for distribution": "Uniikki koodi teeman jakeluun", "Parent Theme": "Isäntäteema", "Database Mode": "Tietokannan tila", "Save changes to this theme in the database instead of the filesystem": "<PERSON><PERSON><PERSON> teeman tiedot tie<PERSON><PERSON><PERSON> tied<PERSON><PERSON><PERSON>an", "-- no parent --": "-- ei isänt<PERSON>ä --", "Theme": "<PERSON><PERSON><PERSON>", "Folders": "<PERSON><PERSON><PERSON>", "Please select the theme folders you would like to export": "Valitse teeman kansiot, jotka haluat viedä", "Theme archive file": "Teemaarkistotiedosto", "Overwrite existing files": "Ylikirjoita olemassaolevat tiedostot", "Untick this box to only import new files": "Poistä tämä käytöstä tuodaksesi vain uudet tiedostot", "Please select the theme folders you would like to import": "<PERSON><PERSON><PERSON> teeman kansiot, jotka haluat tuoda.", "Activate": "Aktivoi", "Customize": "Ku<PERSON><PERSON><PERSON>", "Duplicate": "<PERSON><PERSON>", "Duplicate Theme": "<PERSON><PERSON> teema", "Theme Duplicated!": "<PERSON><PERSON> monistettu!", "Manage": "<PERSON><PERSON><PERSON>", "Manage Theme": "<PERSON><PERSON><PERSON> tee<PERSON>a", "Edit Properties": "Muokkaa o<PERSON>", "Save Properties": "<PERSON><PERSON><PERSON>", "Import": "<PERSON><PERSON><PERSON>", "Import Theme": "<PERSON><PERSON> teema", "Theme Imported!": "<PERSON><PERSON> tuotu!", "Export": "Vie", "Export Theme": "<PERSON><PERSON> teema", "Delete": "Poista", "Delete this theme? It cannot be undone!": "Positetaanko tämä teema? Toimintoa ei voi perua!", "Cannot delete the active theme, try making another theme active first.": "Aktiivista teemaa ei voida poistaa. <PERSON>ta ensin toinen teema k<PERSON>töön", "Theme deleted!": "Te<PERSON> poistettu!", "Create Theme": "<PERSON><PERSON> teema", "Create": "<PERSON><PERSON>", "Create a New Blank Theme": "<PERSON><PERSON> uusi tyhjä teema", "Theme Created!": "<PERSON><PERSON> luotu!", "Please specify a name for the theme.": "<PERSON> tee<PERSON> nimi.", "Name can contain only digits, Latin letters and the following symbols: _-": "Nimi voi sisältää ainoastaan numeroja, latina<PERSON>sia kirjaimia sekä seuraavia merkkejä: _-", "Desired theme directory already exists.": "<PERSON><PERSON><PERSON> on jo o<PERSON><PERSON>a.", "Theme directory": "Teemakansio", "Provide a new directory name for the duplicated theme.": "<PERSON> uusi hake<PERSON>o monist<PERSON><PERSON>e teemal<PERSON>.", "Find More Themes": "Etsi lisää teemoja", "Saving Theme...": "Tallenne<PERSON>an teemaa...", "Return to Themes List": "<PERSON><PERSON><PERSON>", "No themes found": "Teemoja ei lö<PERSON>yt", "Seed Theme": "<PERSON>aa teema", "Seed the specified theme with blueprints, translations and data.": "<PERSON>aa tietty teema blueprinttien, käännöksien ja datan kanssa.", "Theme Seeded!": "<PERSON><PERSON> jaettu!", "Select Page Link": "<PERSON><PERSON><PERSON>", "Search all references...": "<PERSON>tsi kaikki esii<PERSON>t...", "CMS Page": "CMS-sivu", "Link": "<PERSON><PERSON>", "Please select a CMS page": "Valitse CMS-sivu", "Please select a page reference": "Valitse sivureferenssi", "Please enter a valid URL": "<PERSON>-osoite o<PERSON><PERSON><PERSON> muodos<PERSON>", "Delete Theme": "Poista teema", "Activate Theme": "Akt<PERSON>i teema", "Manage Partials": "Hallitse o<PERSON>", "No Title": "<PERSON><PERSON>", "A unique name given to this component when using it in the page or layout code.": "Uniikki nimi komponentille kun käytössä sivulla tai ulkoasun koodissa.", "Alias": "<PERSON><PERSON>", "Component aliases are required and can contain only Latin symbols, digits, and underscores. The aliases should start with a Latin symbol.": "Komponentin alias on vaa<PERSON>tu ja se voi sisältää ainoastaan latinalaisia kirjaimia, numeroita ja alaviivoja. Aliaksien pitäisi alkaa latinalaisella kirjaisimella.", "Unnamed": "Nimeämätön", "No description provided": "<PERSON>i annettua kuvausta", "No snippets found": "<PERSON>sia ei l<PERSON>y", "Search...": "Hae...", "Snippet with the requested code :code was not found in the theme.": "Osaa pyydetyllä koodilla :code ei löytynyt teemasta.", "Snippet": "Osat", "Enter a code to make this partial available as a snippet in content editors.": "<PERSON><PERSON><PERSON><PERSON><PERSON> kood<PERSON>, jotta täm<PERSON> osio on käytettävissä Staattisten sivujen -lisäosassa.", "Please enter the snippet code": "Ole hyvä ja lisää koodin pätkä", "Snippet Code": "<PERSON><PERSON> koodi", "The name is displayed in the snippet list in the editor when a snippet is added.": "<PERSON><PERSON><PERSON> osiolistassa Staattisten sivujen -sivupalkissa ja sivuilla kun osa on lisätty.", "Please enter the snippet name": "<PERSON><PERSON>k<PERSON><PERSON><PERSON> on oltava nimi", "Property Title": "Ominaisuuden <PERSON>", "Please provide the property title": "Ole hyvä ja lisää ominaisuuden otsikko", "Code": "<PERSON><PERSON><PERSON>", "Please provide the property name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> nimi on pakollinen", "Property code should start with a Latin letter and can contain only Latin letters, digits and underscores": "Ominaisuuden koodin tulisi alkaa latinalaisella kirjaimella ja voi sisältää ainoastaan latinalaisia merkkejä ja kokonaislukuja", "Type": "Tyyppi", "Select": "Valitse", "String": "Merkkijono", "Checkbox": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Dropdown": "Alasvetovalikko", "Please select the property type": "Valitse ominaisuuden tyyppi", "Default": "<PERSON><PERSON>", "Options": "Vaihtoehdot", "Define a Snippet Partial": "Määrittele pätkäosio", "Invalid drop-down option key: :key. Option keys can contain only digits, Latin letters and characters _ and -": "Kel<PERSON>ton alasvetovalikon vaihtoehtoavain :key. Vaihtoehtojen avaimet voivat sisältää ainoastaan kokonai<PERSON>luk<PERSON>ja, la<PERSON><PERSON><PERSON> merkkej<PERSON>, ja merkkejä _ ja -", "The description is displayed in the snippet list in the editor when a snippet is added.": "Kuvaus näytetään editorin pätkälistalla, kun pätk<PERSON> on lisätty.", "Details": "Yksityiskohdat"}