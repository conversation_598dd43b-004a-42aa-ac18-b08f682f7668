[{"id": 8, "name": "<PERSON><PERSON> (OCMS)", "code": "header-ocms", "content_html": "<tr>\n    <td class=\"header\">\n        {% if companyLogo %}\n            <a href=\"{{ appUrl }}\" style=\"display: inline-block;\">\n                <div class=\"logo\">\n                    <img src=\"{{ companyLogo }}\" alt=\"{{ companyName }}\">\n                </div>\n            </a>\n        {% else %}\n            <span>\n                {{ body }}\n            </span>\n        {% endif %}\n    </td>\n</tr>", "content_text": "*** {{ body|trim }} <{{ url }}>", "is_custom": 1, "created_at": "2024-06-06 07:55:46", "updated_at": "2024-07-10 14:38:54"}, {"id": 9, "name": "<PERSON>er (OMCS)", "code": "footer-ocms", "content_html": "<tr>\n    <td>\n        <table class=\"footer\" align=\"center\" width=\"570\" cellpadding=\"0\" cellspacing=\"0\" style=\"margin-top: 20px; padding: 20px; color: #666666; font-family: Arial, sans-serif; font-size: 12px; line-height: 18px;\">\n            <!-- Bedrijfsinformatie -->\n            <tr>\n                <td class=\"content-cell\" align=\"center\">\n                    {% if companyData.name %}\n                        <strong><a href=\"{{ appUrl }}\" style=\"color: #666666; text-decoration: underline;\">{{ companyData.name }}</a></strong><br>\n                    {% endif %}\n                    \n                    {% if companyData.street or companyData.zipcode %}\n                        {{ companyData.street }} {{ companyData.housenumber }}{{ companyData.addition }}<br>\n                        {{ companyData.zipcode }} {{ companyData.city }}<br>\n                    {% endif %}\n                    \n                    {% if companyData.telephone %}\n                        Tel: {{ companyData.telephone }}<br>\n                    {% endif %}\n                    \n                    {% if companyData.email %}\n                        E-mail: <a href=\"mailto:{{ companyData.email }}\" style=\"color: #666666; text-decoration: underline;\">{{ companyData.email }}</a><br>\n                    {% endif %}\n                    \n                    {% if companyData.kvk or companyData.btw %}\n                        {% if companyData.kvk %}KvK: {{ companyData.kvk }}{% endif %}\n                        {% if companyData.kvk and companyData.btw %} | {% endif %}\n                        {% if companyData.btw %}BTW: {{ companyData.btw }}{% endif %}\n                        <br>\n                    {% endif %}\n                </td>\n            </tr>\n            \n            <!-- Copyright -->\n            <tr>\n                <td class=\"content-cell\" align=\"center\" style=\"padding-top: 10px;\">\n                    © {{ \"now\"|date(\"Y\") }} {{ companyData.name ?: appName }}. Alle rechten voorbehouden.\n                </td>\n            </tr>\n        </table>\n    </td>\n</tr>", "content_text": "-------------------\n{{ body|trim }}", "is_custom": 1, "created_at": "2024-06-06 07:55:46", "updated_at": "2025-03-07 10:39:49"}]