!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var e;"undefined"!=typeof window?e=window:"undefined"!=typeof global?e=global:"undefined"!=typeof self&&(e=self),e.Promise=t()}}((function(){return function t(e,n,r){function i(s,a){if(!n[s]){if(!e[s]){var c="function"==typeof _dereq_&&_dereq_;if(!a&&c)return c(s,!0);if(o)return o(s,!0);var l=new Error("Cannot find module '"+s+"'");throw l.code="MODULE_NOT_FOUND",l}var u=n[s]={exports:{}};e[s][0].call(u.exports,(function(t){var n=e[s][1][t];return i(n||t)}),u,u.exports,t,e,n,r)}return n[s].exports}for(var o="function"==typeof _dereq_&&_dereq_,s=0;s<r.length;s++)i(r[s]);return i}({1:[function(t,e,n){"use strict";e.exports=function(t){function e(t){var e=new n(t),r=e.promise();return e.setHowMany(1),e.setUnwrap(),e.init(),r}var n=t._SomePromiseArray;t.any=function(t){return e(t)},t.prototype.any=function(){return e(this)}}},{}],2:[function(t,e,n){"use strict";function r(){this._customScheduler=!1,this._isTickUsed=!1,this._lateQueue=new c(16),this._normalQueue=new c(16),this._haveDrainedQueues=!1;var t=this;this.drainQueues=function(){t._drainQueues()},this._schedule=a}function i(t){for(;t.length()>0;)o(t)}function o(t){var e=t.shift();if("function"!=typeof e)e._settlePromises();else{var n=t.shift(),r=t.shift();e.call(n,r)}}var s;try{throw new Error}catch(t){s=t}var a=t("./schedule"),c=t("./queue");r.prototype.setScheduler=function(t){var e=this._schedule;return this._schedule=t,this._customScheduler=!0,e},r.prototype.hasCustomScheduler=function(){return this._customScheduler},r.prototype.haveItemsQueued=function(){return this._isTickUsed||this._haveDrainedQueues},r.prototype.fatalError=function(t,e){e?(process.stderr.write("Fatal "+(t instanceof Error?t.stack:t)+"\n"),process.exit(2)):this.throwLater(t)},r.prototype.throwLater=function(t,e){if(1===arguments.length&&(e=t,t=function(){throw e}),"undefined"!=typeof setTimeout)setTimeout((function(){t(e)}),0);else try{this._schedule((function(){t(e)}))}catch(t){throw new Error("No async scheduler available\n\n    See http://goo.gl/MqrFmX\n")}},r.prototype.invokeLater=function(t,e,n){this._lateQueue.push(t,e,n),this._queueTick()},r.prototype.invoke=function(t,e,n){this._normalQueue.push(t,e,n),this._queueTick()},r.prototype.settlePromises=function(t){this._normalQueue._pushOne(t),this._queueTick()},r.prototype._drainQueues=function(){i(this._normalQueue),this._reset(),this._haveDrainedQueues=!0,i(this._lateQueue)},r.prototype._queueTick=function(){this._isTickUsed||(this._isTickUsed=!0,this._schedule(this.drainQueues))},r.prototype._reset=function(){this._isTickUsed=!1},e.exports=r,e.exports.firstLineError=s},{"./queue":26,"./schedule":29}],3:[function(t,e,n){"use strict";e.exports=function(t,e,n,r){var i=!1,o=function(t,e){this._reject(e)},s=function(t,e){e.promiseRejectionQueued=!0,e.bindingPromise._then(o,o,null,this,t)},a=function(t,e){0==(50397184&this._bitField)&&this._resolveCallback(e.target)},c=function(t,e){e.promiseRejectionQueued||this._reject(t)};t.prototype.bind=function(o){i||(i=!0,t.prototype._propagateFrom=r.propagateFromFunction(),t.prototype._boundValue=r.boundValueFunction());var l=n(o),u=new t(e);u._propagateFrom(this,1);var p=this._target();if(u._setBoundTo(l),l instanceof t){var f={promiseRejectionQueued:!1,promise:u,target:p,bindingPromise:l};p._then(e,s,void 0,u,f),l._then(a,c,void 0,u,f),u._setOnCancel(l)}else u._resolveCallback(p);return u},t.prototype._setBoundTo=function(t){void 0!==t?(this._bitField=2097152|this._bitField,this._boundTo=t):this._bitField=-2097153&this._bitField},t.prototype._isBound=function(){return 2097152==(2097152&this._bitField)},t.bind=function(e,n){return t.resolve(n).bind(e)}}},{}],4:[function(t,e,n){"use strict";var r;"undefined"!=typeof Promise&&(r=Promise);var i=t("./promise")();i.noConflict=function(){try{Promise===i&&(Promise=r)}catch(t){}return i},e.exports=i},{"./promise":22}],5:[function(t,e,n){"use strict";var r=Object.create;if(r){var i=r(null),o=r(null);i[" size"]=o[" size"]=0}e.exports=function(e){function n(t,n){var r;if(null!=t&&(r=t[n]),"function"!=typeof r){var i="Object "+s.classString(t)+" has no method '"+s.toString(n)+"'";throw new e.TypeError(i)}return r}function r(t){return n(t,this.pop()).apply(t,this)}function i(t){return t[this]}function o(t){var e=+this;return 0>e&&(e=Math.max(0,e+t.length)),t[e]}var s=t("./util"),a=s.canEvaluate;s.isIdentifier,e.prototype.call=function(t){var e=[].slice.call(arguments,1);return e.push(t),this._then(r,void 0,void 0,e,void 0)},e.prototype.get=function(t){var e;if("number"==typeof t)e=o;else if(a){var n=undefined(t);e=null!==n?n:i}else e=i;return this._then(e,void 0,void 0,t,void 0)}}},{"./util":36}],6:[function(t,e,n){"use strict";e.exports=function(e,n,r,i){var o=t("./util"),s=o.tryCatch,a=o.errorObj,c=e._async;e.prototype.break=e.prototype.cancel=function(){if(!i.cancellation())return this._warn("cancellation is disabled");for(var t=this,e=t;t._isCancellable();){if(!t._cancelBy(e)){e._isFollowing()?e._followee().cancel():e._cancelBranched();break}var n=t._cancellationParent;if(null==n||!n._isCancellable()){t._isFollowing()?t._followee().cancel():t._cancelBranched();break}t._isFollowing()&&t._followee().cancel(),t._setWillBeCancelled(),e=t,t=n}},e.prototype._branchHasCancelled=function(){this._branchesRemainingToCancel--},e.prototype._enoughBranchesHaveCancelled=function(){return void 0===this._branchesRemainingToCancel||this._branchesRemainingToCancel<=0},e.prototype._cancelBy=function(t){return t===this?(this._branchesRemainingToCancel=0,this._invokeOnCancel(),!0):(this._branchHasCancelled(),!!this._enoughBranchesHaveCancelled()&&(this._invokeOnCancel(),!0))},e.prototype._cancelBranched=function(){this._enoughBranchesHaveCancelled()&&this._cancel()},e.prototype._cancel=function(){this._isCancellable()&&(this._setCancelled(),c.invoke(this._cancelPromises,this,void 0))},e.prototype._cancelPromises=function(){this._length()>0&&this._settlePromises()},e.prototype._unsetOnCancel=function(){this._onCancelField=void 0},e.prototype._isCancellable=function(){return this.isPending()&&!this._isCancelled()},e.prototype.isCancellable=function(){return this.isPending()&&!this.isCancelled()},e.prototype._doInvokeOnCancel=function(t,e){if(o.isArray(t))for(var n=0;n<t.length;++n)this._doInvokeOnCancel(t[n],e);else if(void 0!==t)if("function"==typeof t){if(!e){var r=s(t).call(this._boundValue());r===a&&(this._attachExtraTrace(r.e),c.throwLater(r.e))}}else t._resultCancelled(this)},e.prototype._invokeOnCancel=function(){var t=this._onCancel();this._unsetOnCancel(),c.invoke(this._doInvokeOnCancel,this,t)},e.prototype._invokeInternalOnCancel=function(){this._isCancellable()&&(this._doInvokeOnCancel(this._onCancel(),!0),this._unsetOnCancel())},e.prototype._resultCancelled=function(){this.cancel()}}},{"./util":36}],7:[function(t,e,n){"use strict";e.exports=function(e){var n=t("./util"),r=t("./es5").keys,i=n.tryCatch,o=n.errorObj;return function(t,s,a){return function(c){var l=a._boundValue();t:for(var u=0;u<t.length;++u){var p=t[u];if(p===Error||null!=p&&p.prototype instanceof Error){if(c instanceof p)return i(s).call(l,c)}else if("function"==typeof p){var f=i(p).call(l,c);if(f===o)return f;if(f)return i(s).call(l,c)}else if(n.isObject(c)){for(var h=r(p),_=0;_<h.length;++_){var d=h[_];if(p[d]!=c[d])continue t}return i(s).call(l,c)}}return e}}}},{"./es5":13,"./util":36}],8:[function(t,e,n){"use strict";e.exports=function(t){function e(){this._trace=new e.CapturedTrace(n())}function n(){var t=i.length-1;return t>=0?i[t]:void 0}var r=!1,i=[];return t.prototype._promiseCreated=function(){},t.prototype._pushContext=function(){},t.prototype._popContext=function(){return null},t._peekContext=t.prototype._peekContext=function(){},e.prototype._pushContext=function(){void 0!==this._trace&&(this._trace._promiseCreated=null,i.push(this._trace))},e.prototype._popContext=function(){if(void 0!==this._trace){var t=i.pop(),e=t._promiseCreated;return t._promiseCreated=null,e}return null},e.CapturedTrace=null,e.create=function(){return r?new e:void 0},e.deactivateLongStackTraces=function(){},e.activateLongStackTraces=function(){var i=t.prototype._pushContext,o=t.prototype._popContext,s=t._peekContext,a=t.prototype._peekContext,c=t.prototype._promiseCreated;e.deactivateLongStackTraces=function(){t.prototype._pushContext=i,t.prototype._popContext=o,t._peekContext=s,t.prototype._peekContext=a,t.prototype._promiseCreated=c,r=!1},r=!0,t.prototype._pushContext=e.prototype._pushContext,t.prototype._popContext=e.prototype._popContext,t._peekContext=t.prototype._peekContext=n,t.prototype._promiseCreated=function(){var t=this._peekContext();t&&null==t._promiseCreated&&(t._promiseCreated=this)}},e}},{}],9:[function(t,e,n){"use strict";e.exports=function(e,n,r,i){function o(t,e){return{promise:e}}function s(){return!1}function a(t,e,n){var r=this;try{t(e,n,(function(t){if("function"!=typeof t)throw new TypeError("onCancel must be a function, got: "+O.toString(t));r._attachCancellationCallback(t)}))}catch(t){return t}}function c(t){if(!this._isCancellable())return this;var e=this._onCancel();void 0!==e?O.isArray(e)?e.push(t):this._setOnCancel([e,t]):this._setOnCancel(t)}function l(){return this._onCancelField}function u(t){this._onCancelField=t}function p(){this._cancellationParent=void 0,this._onCancelField=void 0}function f(t,e){if(0!=(1&e)){this._cancellationParent=t;var n=t._branchesRemainingToCancel;void 0===n&&(n=0),t._branchesRemainingToCancel=n+1}0!=(2&e)&&t._isBound()&&this._setBoundTo(t._boundTo)}function h(){var t=this._boundTo;return void 0!==t&&t instanceof e?t.isFulfilled()?t.value():void 0:t}function _(){this._trace=new E(this._peekContext())}function d(t,e){if(V(t)){var n=this._trace;if(void 0!==n&&e&&(n=n._parent),void 0!==n)n.attachExtraTrace(t);else if(!t.__stackCleaned__){var r=m(t);O.notEnumerableProp(t,"stack",r.message+"\n"+r.stack.join("\n")),O.notEnumerableProp(t,"__stackCleaned__",!0)}}}function v(){this._trace=void 0}function y(t,n,r){if(et.warnings){var i,o=new S(t);if(n)r._attachExtraTrace(o);else if(et.longStackTraces&&(i=e._peekContext()))i.attachExtraTrace(o);else{var s=m(o);o.stack=s.message+"\n"+s.stack.join("\n")}K("warning",o)||b(o,"",!0)}}function g(t){for(var e=[],n=0;n<t.length;++n){var r=t[n],i="    (No stack trace)"===r||L.test(r),o=i&&Y(r);i&&!o&&(U&&" "!==r.charAt(0)&&(r="    "+r),e.push(r))}return e}function m(t){var e=t.stack,n=t.toString();return e="string"==typeof e&&e.length>0?function(t){for(var e=t.stack.replace(/\s+$/g,"").split("\n"),n=0;n<e.length;++n){var r=e[n];if("    (No stack trace)"===r||L.test(r))break}return n>0&&"SyntaxError"!=t.name&&(e=e.slice(n)),e}(t):["    (No stack trace)"],{message:n,stack:"SyntaxError"==t.name?e:g(e)}}function b(t,e,n){if("undefined"!=typeof console){var r;if(O.isObject(t)){var i=t.stack;r=e+N(i,t)}else r=e+String(t);"function"==typeof T?T(r,n):("function"==typeof console.log||"object"==typeof console.log)&&console.log(r)}}function w(t,e,n,r){var i=!1;try{"function"==typeof e&&(i=!0,"rejectionHandled"===t?e(r):e(n,r))}catch(t){R.throwLater(t)}"unhandledRejection"===t?K(t,n,r)||i||b(n,"Unhandled rejection "):K(t,r)}function C(t){var e;if("function"==typeof t)e="[function "+(t.name||"anonymous")+"]";else{e=t&&"function"==typeof t.toString?t.toString():O.toString(t);if(/\[object [a-zA-Z0-9$_]+\]/.test(e))try{e=JSON.stringify(t)}catch(t){}0===e.length&&(e="(empty array)")}return"(<"+function(t){var e=41;return t.length<e?t:t.substr(0,e-3)+"..."}(e)+">, no stack trace)"}function j(){return"function"==typeof tt}function k(t){var e=t.match(Z);return e?{fileName:e[1],line:parseInt(e[2],10)}:void 0}function E(t){this._parent=t,this._promisesCreated=0;var e=this._length=1+(void 0===t?0:t._length);tt(this,E),e>32&&this.uncycle()}var F,x,T,P,R=e._async,S=t("./errors").Warning,O=t("./util"),A=t("./es5"),V=O.canAttachTrace,H=/[\\\/]bluebird[\\\/]js[\\\/](release|debug|instrumented)/,D=/\((?:timers\.js):\d+:\d+\)/,I=/[\/<\(](.+?):(\d+):(\d+)\)?\s*$/,L=null,N=null,U=!1,B=!(0==O.env("BLUEBIRD_DEBUG")||!O.env("BLUEBIRD_DEBUG")&&"development"!==O.env("NODE_ENV")),M=!(0==O.env("BLUEBIRD_WARNINGS")||!B&&!O.env("BLUEBIRD_WARNINGS")),q=!(0==O.env("BLUEBIRD_LONG_STACK_TRACES")||!B&&!O.env("BLUEBIRD_LONG_STACK_TRACES")),$=0!=O.env("BLUEBIRD_W_FORGOTTEN_RETURN")&&(M||!!O.env("BLUEBIRD_W_FORGOTTEN_RETURN"));!function(){function t(){for(var t=0;t<r.length;++t)r[t]._notifyUnhandledRejection();n()}function n(){r.length=0}var r=[];P=function(e){r.push(e),setTimeout(t,1)},A.defineProperty(e,"_unhandledRejectionCheck",{value:t}),A.defineProperty(e,"_unhandledRejectionClear",{value:n})}(),e.prototype.suppressUnhandledRejections=function(){var t=this._target();t._bitField=-1048577&t._bitField|524288},e.prototype._ensurePossibleRejectionHandled=function(){0==(524288&this._bitField)&&(this._setRejectionIsUnhandled(),P(this))},e.prototype._notifyUnhandledRejectionIsHandled=function(){w("rejectionHandled",F,void 0,this)},e.prototype._setReturnedNonUndefined=function(){this._bitField=268435456|this._bitField},e.prototype._returnedNonUndefined=function(){return 0!=(268435456&this._bitField)},e.prototype._notifyUnhandledRejection=function(){if(this._isRejectionUnhandled()){var t=this._settledValue();this._setUnhandledRejectionIsNotified(),w("unhandledRejection",x,t,this)}},e.prototype._setUnhandledRejectionIsNotified=function(){this._bitField=262144|this._bitField},e.prototype._unsetUnhandledRejectionIsNotified=function(){this._bitField=-262145&this._bitField},e.prototype._isUnhandledRejectionNotified=function(){return(262144&this._bitField)>0},e.prototype._setRejectionIsUnhandled=function(){this._bitField=1048576|this._bitField},e.prototype._unsetRejectionIsUnhandled=function(){this._bitField=-1048577&this._bitField,this._isUnhandledRejectionNotified()&&(this._unsetUnhandledRejectionIsNotified(),this._notifyUnhandledRejectionIsHandled())},e.prototype._isRejectionUnhandled=function(){return(1048576&this._bitField)>0},e.prototype._warn=function(t,e,n){return y(t,e,n||this)},e.onPossiblyUnhandledRejection=function(t){var n=e._getContext();x=O.contextBind(n,t)},e.onUnhandledRejectionHandled=function(t){var n=e._getContext();F=O.contextBind(n,t)};var Q=function(){};e.longStackTraces=function(){if(R.haveItemsQueued()&&!et.longStackTraces)throw new Error("cannot enable long stack traces after promises have been created\n\n    See http://goo.gl/MqrFmX\n");if(!et.longStackTraces&&j()){var t=e.prototype._captureStackTrace,r=e.prototype._attachExtraTrace,i=e.prototype._dereferenceTrace;et.longStackTraces=!0,Q=function(){if(R.haveItemsQueued()&&!et.longStackTraces)throw new Error("cannot enable long stack traces after promises have been created\n\n    See http://goo.gl/MqrFmX\n");e.prototype._captureStackTrace=t,e.prototype._attachExtraTrace=r,e.prototype._dereferenceTrace=i,n.deactivateLongStackTraces(),et.longStackTraces=!1},e.prototype._captureStackTrace=_,e.prototype._attachExtraTrace=d,e.prototype._dereferenceTrace=v,n.activateLongStackTraces()}},e.hasLongStackTraces=function(){return et.longStackTraces&&j()};var G={unhandledrejection:{before:function(){var t=O.global.onunhandledrejection;return O.global.onunhandledrejection=null,t},after:function(t){O.global.onunhandledrejection=t}},rejectionhandled:{before:function(){var t=O.global.onrejectionhandled;return O.global.onrejectionhandled=null,t},after:function(t){O.global.onrejectionhandled=t}}},z=function(){var t=function(t,e){if(!t)return!O.global.dispatchEvent(e);var n;try{return n=t.before(),!O.global.dispatchEvent(e)}finally{t.after(n)}};try{if("function"==typeof CustomEvent){var e=new CustomEvent("CustomEvent");return O.global.dispatchEvent(e),function(e,n){e=e.toLowerCase();var r=new CustomEvent(e,{detail:n,cancelable:!0});return A.defineProperty(r,"promise",{value:n.promise}),A.defineProperty(r,"reason",{value:n.reason}),t(G[e],r)}}if("function"==typeof Event){e=new Event("CustomEvent");return O.global.dispatchEvent(e),function(e,n){e=e.toLowerCase();var r=new Event(e,{cancelable:!0});return r.detail=n,A.defineProperty(r,"promise",{value:n.promise}),A.defineProperty(r,"reason",{value:n.reason}),t(G[e],r)}}return(e=document.createEvent("CustomEvent")).initCustomEvent("testingtheevent",!1,!0,{}),O.global.dispatchEvent(e),function(e,n){e=e.toLowerCase();var r=document.createEvent("CustomEvent");return r.initCustomEvent(e,!1,!0,n),t(G[e],r)}}catch(t){}return function(){return!1}}(),X=O.isNode?function(){return process.emit.apply(process,arguments)}:O.global?function(t){var e="on"+t.toLowerCase(),n=O.global[e];return!!n&&(n.apply(O.global,[].slice.call(arguments,1)),!0)}:function(){return!1},W={promiseCreated:o,promiseFulfilled:o,promiseRejected:o,promiseResolved:o,promiseCancelled:o,promiseChained:function(t,e,n){return{promise:e,child:n}},warning:function(t,e){return{warning:e}},unhandledRejection:function(t,e,n){return{reason:e,promise:n}},rejectionHandled:o},K=function(t){var e=!1;try{e=X.apply(null,arguments)}catch(t){R.throwLater(t),e=!0}var n=!1;try{n=z(t,W[t].apply(null,arguments))}catch(t){R.throwLater(t),n=!0}return n||e};e.config=function(t){if("longStackTraces"in(t=Object(t))&&(t.longStackTraces?e.longStackTraces():!t.longStackTraces&&e.hasLongStackTraces()&&Q()),"warnings"in t){var n=t.warnings;et.warnings=!!n,$=et.warnings,O.isObject(n)&&"wForgottenReturn"in n&&($=!!n.wForgottenReturn)}if("cancellation"in t&&t.cancellation&&!et.cancellation){if(R.haveItemsQueued())throw new Error("cannot enable cancellation after promises are in use");e.prototype._clearCancellationData=p,e.prototype._propagateFrom=f,e.prototype._onCancel=l,e.prototype._setOnCancel=u,e.prototype._attachCancellationCallback=c,e.prototype._execute=a,J=f,et.cancellation=!0}if("monitoring"in t&&(t.monitoring&&!et.monitoring?(et.monitoring=!0,e.prototype._fireEvent=K):!t.monitoring&&et.monitoring&&(et.monitoring=!1,e.prototype._fireEvent=s)),"asyncHooks"in t&&O.nodeSupportsAsyncResource){var o=et.asyncHooks,h=!!t.asyncHooks;o!==h&&(et.asyncHooks=h,h?r():i())}return e},e.prototype._fireEvent=s,e.prototype._execute=function(t,e,n){try{t(e,n)}catch(t){return t}},e.prototype._onCancel=function(){},e.prototype._setOnCancel=function(t){},e.prototype._attachCancellationCallback=function(t){},e.prototype._captureStackTrace=function(){},e.prototype._attachExtraTrace=function(){},e.prototype._dereferenceTrace=function(){},e.prototype._clearCancellationData=function(){},e.prototype._propagateFrom=function(t,e){};var J=function(t,e){0!=(2&e)&&t._isBound()&&this._setBoundTo(t._boundTo)},Y=function(){return!1},Z=/[\/<\(]([^:\/]+):(\d+):(?:\d+)\)?\s*$/;O.inherits(E,Error),n.CapturedTrace=E,E.prototype.uncycle=function(){var t=this._length;if(!(2>t)){for(var e=[],n={},r=0,i=this;void 0!==i;++r)e.push(i),i=i._parent;for(r=(t=this._length=r)-1;r>=0;--r){var o=e[r].stack;void 0===n[o]&&(n[o]=r)}for(r=0;t>r;++r){var s=n[e[r].stack];if(void 0!==s&&s!==r){s>0&&(e[s-1]._parent=void 0,e[s-1]._length=1),e[r]._parent=void 0,e[r]._length=1;var a=r>0?e[r-1]:this;t-1>s?(a._parent=e[s+1],a._parent.uncycle(),a._length=a._parent._length+1):(a._parent=void 0,a._length=1);for(var c=a._length+1,l=r-2;l>=0;--l)e[l]._length=c,c++;return}}}},E.prototype.attachExtraTrace=function(t){if(!t.__stackCleaned__){this.uncycle();for(var e=m(t),n=e.message,r=[e.stack],i=this;void 0!==i;)r.push(g(i.stack.split("\n"))),i=i._parent;(function(t){for(var e=t[0],n=1;n<t.length;++n){for(var r=t[n],i=e.length-1,o=e[i],s=-1,a=r.length-1;a>=0;--a)if(r[a]===o){s=a;break}for(a=s;a>=0;--a){var c=r[a];if(e[i]!==c)break;e.pop(),i--}e=r}})(r),function(t){for(var e=0;e<t.length;++e)(0===t[e].length||e+1<t.length&&t[e][0]===t[e+1][0])&&(t.splice(e,1),e--)}(r),O.notEnumerableProp(t,"stack",function(t,e){for(var n=0;n<e.length-1;++n)e[n].push("From previous event:"),e[n]=e[n].join("\n");return n<e.length&&(e[n]=e[n].join("\n")),t+"\n"+e.join("\n")}(n,r)),O.notEnumerableProp(t,"__stackCleaned__",!0)}};var tt=function(){var t=/^\s*at\s*/,e=function(t,e){return"string"==typeof t?t:void 0!==e.name&&void 0!==e.message?e.toString():C(e)};if("number"==typeof Error.stackTraceLimit&&"function"==typeof Error.captureStackTrace){Error.stackTraceLimit+=6,L=t,N=e;var n=Error.captureStackTrace;return Y=function(t){return H.test(t)},function(t,e){Error.stackTraceLimit+=6,n(t,e),Error.stackTraceLimit-=6}}var r,i=new Error;if("string"==typeof i.stack&&i.stack.split("\n")[0].indexOf("stackDetection@")>=0)return L=/@/,N=e,U=!0,function(t){t.stack=(new Error).stack};try{throw new Error}catch(t){r="stack"in t}return"stack"in i||!r||"number"!=typeof Error.stackTraceLimit?(N=function(t,e){return"string"==typeof t?t:"object"!=typeof e&&"function"!=typeof e||void 0===e.name||void 0===e.message?C(e):e.toString()},null):(L=t,N=e,function(t){Error.stackTraceLimit+=6;try{throw new Error}catch(e){t.stack=e.stack}Error.stackTraceLimit-=6})}();"undefined"!=typeof console&&void 0!==console.warn&&(T=function(t){console.warn(t)},O.isNode&&process.stderr.isTTY?T=function(t,e){var n=e?"[33m":"[31m";console.warn(n+t+"[0m\n")}:O.isNode||"string"!=typeof(new Error).stack||(T=function(t,e){console.warn("%c"+t,e?"color: darkorange":"color: red")}));var et={warnings:M,longStackTraces:!1,cancellation:!1,monitoring:!1,asyncHooks:!1};return q&&e.longStackTraces(),{asyncHooks:function(){return et.asyncHooks},longStackTraces:function(){return et.longStackTraces},warnings:function(){return et.warnings},cancellation:function(){return et.cancellation},monitoring:function(){return et.monitoring},propagateFromFunction:function(){return J},boundValueFunction:function(){return h},checkForgottenReturns:function(t,e,n,r,i){if(void 0===t&&null!==e&&$){if(void 0!==i&&i._returnedNonUndefined())return;if(0==(65535&r._bitField))return;n&&(n+=" ");var o="",s="";if(e._trace){for(var a=e._trace.stack.split("\n"),c=g(a),l=c.length-1;l>=0;--l){var u=c[l];if(!D.test(u)){var p=u.match(I);p&&(o="at "+p[1]+":"+p[2]+":"+p[3]+" ");break}}if(c.length>0){var f=c[0];for(l=0;l<a.length;++l)if(a[l]===f){l>0&&(s="\n"+a[l-1]);break}}}var h="a promise was created in a "+n+"handler "+o+"but was not returned from it, see http://goo.gl/rRqMUw"+s;r._warn(h,!0,e)}},setBounds:function(t,e){if(j()){for(var n,r,i=(t.stack||"").split("\n"),o=(e.stack||"").split("\n"),s=-1,a=-1,c=0;c<i.length;++c){if(l=k(i[c])){n=l.fileName,s=l.line;break}}for(c=0;c<o.length;++c){var l;if(l=k(o[c])){r=l.fileName,a=l.line;break}}0>s||0>a||!n||!r||n!==r||s>=a||(Y=function(t){if(H.test(t))return!0;var e=k(t);return!!(e&&e.fileName===n&&s<=e.line&&e.line<=a)})}},warn:y,deprecated:function(t,e){var n=t+" is deprecated and will be removed in a future version.";return e&&(n+=" Use "+e+" instead."),y(n)},CapturedTrace:E,fireDomEvent:z,fireGlobalEvent:X}}},{"./errors":12,"./es5":13,"./util":36}],10:[function(t,e,n){"use strict";e.exports=function(t){function e(){return this.value}function n(){throw this.reason}t.prototype.return=t.prototype.thenReturn=function(n){return n instanceof t&&n.suppressUnhandledRejections(),this._then(e,void 0,void 0,{value:n},void 0)},t.prototype.throw=t.prototype.thenThrow=function(t){return this._then(n,void 0,void 0,{reason:t},void 0)},t.prototype.catchThrow=function(t){if(arguments.length<=1)return this._then(void 0,n,void 0,{reason:t},void 0);var e=arguments[1],r=function(){throw e};return this.caught(t,r)},t.prototype.catchReturn=function(n){if(arguments.length<=1)return n instanceof t&&n.suppressUnhandledRejections(),this._then(void 0,e,void 0,{value:n},void 0);var r=arguments[1];r instanceof t&&r.suppressUnhandledRejections();var i=function(){return r};return this.caught(n,i)}}},{}],11:[function(t,e,n){"use strict";e.exports=function(t,e){function n(){return i(this)}var r=t.reduce,i=t.all;t.prototype.each=function(t){return r(this,t,e,0)._then(n,void 0,void 0,this,void 0)},t.prototype.mapSeries=function(t){return r(this,t,e,e)},t.each=function(t,i){return r(t,i,e,0)._then(n,void 0,void 0,t,void 0)},t.mapSeries=function(t,n){return r(t,n,e,e)}}},{}],12:[function(t,e,n){"use strict";function r(t,e){function n(r){return this instanceof n?(p(this,"message","string"==typeof r?r:e),p(this,"name",t),void(Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):Error.call(this))):new n(r)}return u(n,Error),n}function i(t){return this instanceof i?(p(this,"name","OperationalError"),p(this,"message",t),this.cause=t,this.isOperational=!0,void(t instanceof Error?(p(this,"message",t.message),p(this,"stack",t.stack)):Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor))):new i(t)}var o,s,a=t("./es5"),c=a.freeze,l=t("./util"),u=l.inherits,p=l.notEnumerableProp,f=r("Warning","warning"),h=r("CancellationError","cancellation error"),_=r("TimeoutError","timeout error"),d=r("AggregateError","aggregate error");try{o=TypeError,s=RangeError}catch(t){o=r("TypeError","type error"),s=r("RangeError","range error")}for(var v="join pop push shift unshift slice filter forEach some every map indexOf lastIndexOf reduce reduceRight sort reverse".split(" "),y=0;y<v.length;++y)"function"==typeof Array.prototype[v[y]]&&(d.prototype[v[y]]=Array.prototype[v[y]]);a.defineProperty(d.prototype,"length",{value:0,configurable:!1,writable:!0,enumerable:!0}),d.prototype.isOperational=!0;var g=0;d.prototype.toString=function(){var t=Array(4*g+1).join(" "),e="\n"+t+"AggregateError of:\n";g++,t=Array(4*g+1).join(" ");for(var n=0;n<this.length;++n){for(var r=this[n]===this?"[Circular AggregateError]":this[n]+"",i=r.split("\n"),o=0;o<i.length;++o)i[o]=t+i[o];e+=(r=i.join("\n"))+"\n"}return g--,e},u(i,Error);var m=Error.__BluebirdErrorTypes__;m||(m=c({CancellationError:h,TimeoutError:_,OperationalError:i,RejectionError:i,AggregateError:d}),a.defineProperty(Error,"__BluebirdErrorTypes__",{value:m,writable:!1,enumerable:!1,configurable:!1})),e.exports={Error:Error,TypeError:o,RangeError:s,CancellationError:m.CancellationError,OperationalError:m.OperationalError,TimeoutError:m.TimeoutError,AggregateError:m.AggregateError,Warning:f}},{"./es5":13,"./util":36}],13:[function(t,e,n){var r=function(){"use strict";return void 0===this}();if(r)e.exports={freeze:Object.freeze,defineProperty:Object.defineProperty,getDescriptor:Object.getOwnPropertyDescriptor,keys:Object.keys,names:Object.getOwnPropertyNames,getPrototypeOf:Object.getPrototypeOf,isArray:Array.isArray,isES5:r,propertyIsWritable:function(t,e){var n=Object.getOwnPropertyDescriptor(t,e);return!(n&&!n.writable&&!n.set)}};else{var i={}.hasOwnProperty,o={}.toString,s={}.constructor.prototype,a=function(t){var e=[];for(var n in t)i.call(t,n)&&e.push(n);return e};e.exports={isArray:function(t){try{return"[object Array]"===o.call(t)}catch(t){return!1}},keys:a,names:a,defineProperty:function(t,e,n){return t[e]=n.value,t},getDescriptor:function(t,e){return{value:t[e]}},freeze:function(t){return t},getPrototypeOf:function(t){try{return Object(t).constructor.prototype}catch(t){return s}},isES5:r,propertyIsWritable:function(){return!0}}}},{}],14:[function(t,e,n){"use strict";e.exports=function(t,e){var n=t.map;t.prototype.filter=function(t,r){return n(this,t,r,e)},t.filter=function(t,r,i){return n(t,r,i,e)}}},{}],15:[function(t,e,n){"use strict";e.exports=function(e,n,r){function i(t,e,n){this.promise=t,this.type=e,this.handler=n,this.called=!1,this.cancelPromise=null}function o(t){this.finallyHandler=t}function s(t,e){return null!=t.cancelPromise&&(arguments.length>1?t.cancelPromise._reject(e):t.cancelPromise._cancel(),t.cancelPromise=null,!0)}function a(){return l.call(this,this.promise._target()._settledValue())}function c(t){return s(this,t)?void 0:(f.e=t,f)}function l(t){var i=this.promise,l=this.handler;if(!this.called){this.called=!0;var u=this.isFinallyHandler()?l.call(i._boundValue()):l.call(i._boundValue(),t);if(u===r)return u;if(void 0!==u){i._setReturnedNonUndefined();var h=n(u,i);if(h instanceof e){if(null!=this.cancelPromise){if(h._isCancelled()){var _=new p("late cancellation observer");return i._attachExtraTrace(_),f.e=_,f}h.isPending()&&h._attachCancellationCallback(new o(this))}return h._then(a,c,void 0,this,void 0)}}}return i.isRejected()?(s(this),f.e=t,f):(s(this),t)}var u=t("./util"),p=e.CancellationError,f=u.errorObj,h=t("./catch_filter")(r);return i.prototype.isFinallyHandler=function(){return 0===this.type},o.prototype._resultCancelled=function(){s(this.finallyHandler)},e.prototype._passThrough=function(t,e,n,r){return"function"!=typeof t?this.then():this._then(n,r,void 0,new i(this,e,t),void 0)},e.prototype.lastly=e.prototype.finally=function(t){return this._passThrough(t,0,l,l)},e.prototype.tap=function(t){return this._passThrough(t,1,l)},e.prototype.tapCatch=function(t){var n=arguments.length;if(1===n)return this._passThrough(t,1,void 0,l);var r,i=new Array(n-1),o=0;for(r=0;n-1>r;++r){var s=arguments[r];if(!u.isObject(s))return e.reject(new TypeError("tapCatch statement predicate: expecting an object but got "+u.classString(s)));i[o++]=s}i.length=o;var a=arguments[r];return this._passThrough(h(i,a,this),1,void 0,l)},i}},{"./catch_filter":7,"./util":36}],16:[function(t,e,n){"use strict";e.exports=function(e,n,r,i,o,s){function a(t,n,i,o){if(s.cancellation()){var a=new e(r),c=this._finallyPromise=new e(r);this._promise=a.lastly((function(){return c})),a._captureStackTrace(),a._setOnCancel(this)}else{(this._promise=new e(r))._captureStackTrace()}this._stack=o,this._generatorFunction=t,this._receiver=n,this._generator=void 0,this._yieldHandlers="function"==typeof i?[i].concat(f):f,this._yieldedPromise=null,this._cancellationPhase=!1}var c=t("./errors").TypeError,l=t("./util"),u=l.errorObj,p=l.tryCatch,f=[];l.inherits(a,o),a.prototype._isResolved=function(){return null===this._promise},a.prototype._cleanup=function(){this._promise=this._generator=null,s.cancellation()&&null!==this._finallyPromise&&(this._finallyPromise._fulfill(),this._finallyPromise=null)},a.prototype._promiseCancelled=function(){if(!this._isResolved()){var t;if(void 0!==this._generator.return)this._promise._pushContext(),t=p(this._generator.return).call(this._generator,void 0),this._promise._popContext();else{var n=new e.CancellationError("generator .return() sentinel");e.coroutine.returnSentinel=n,this._promise._attachExtraTrace(n),this._promise._pushContext(),t=p(this._generator.throw).call(this._generator,n),this._promise._popContext()}this._cancellationPhase=!0,this._yieldedPromise=null,this._continue(t)}},a.prototype._promiseFulfilled=function(t){this._yieldedPromise=null,this._promise._pushContext();var e=p(this._generator.next).call(this._generator,t);this._promise._popContext(),this._continue(e)},a.prototype._promiseRejected=function(t){this._yieldedPromise=null,this._promise._attachExtraTrace(t),this._promise._pushContext();var e=p(this._generator.throw).call(this._generator,t);this._promise._popContext(),this._continue(e)},a.prototype._resultCancelled=function(){if(this._yieldedPromise instanceof e){var t=this._yieldedPromise;this._yieldedPromise=null,t.cancel()}},a.prototype.promise=function(){return this._promise},a.prototype._run=function(){this._generator=this._generatorFunction.call(this._receiver),this._receiver=this._generatorFunction=void 0,this._promiseFulfilled(void 0)},a.prototype._continue=function(t){var n=this._promise;if(t===u)return this._cleanup(),this._cancellationPhase?n.cancel():n._rejectCallback(t.e,!1);var r=t.value;if(!0===t.done)return this._cleanup(),this._cancellationPhase?n.cancel():n._resolveCallback(r);var o=i(r,this._promise);if(o instanceof e||(o=function(t,n,r){for(var o=0;o<n.length;++o){r._pushContext();var s=p(n[o])(t);if(r._popContext(),s===u){r._pushContext();var a=e.reject(u.e);return r._popContext(),a}var c=i(s,r);if(c instanceof e)return c}return null}(o,this._yieldHandlers,this._promise),null!==o)){var s=(o=o._target())._bitField;0==(50397184&s)?(this._yieldedPromise=o,o._proxy(this,null)):0!=(33554432&s)?e._async.invoke(this._promiseFulfilled,this,o._value()):0!=(16777216&s)?e._async.invoke(this._promiseRejected,this,o._reason()):this._promiseCancelled()}else this._promiseRejected(new c("A value %s was yielded that could not be treated as a promise\n\n    See http://goo.gl/MqrFmX\n\n".replace("%s",String(r))+"From coroutine:\n"+this._stack.split("\n").slice(1,-7).join("\n")))},e.coroutine=function(t,e){if("function"!=typeof t)throw new c("generatorFunction must be a function\n\n    See http://goo.gl/MqrFmX\n");var n=Object(e).yieldHandler,r=a,i=(new Error).stack;return function(){var e=t.apply(this,arguments),o=new r(void 0,void 0,n,i),s=o.promise();return o._generator=e,o._promiseFulfilled(void 0),s}},e.coroutine.addYieldHandler=function(t){if("function"!=typeof t)throw new c("expecting a function but got "+l.classString(t));f.push(t)},e.spawn=function(t){if(s.deprecated("Promise.spawn()","Promise.coroutine()"),"function"!=typeof t)return n("generatorFunction must be a function\n\n    See http://goo.gl/MqrFmX\n");var r=new a(t,this),i=r.promise();return r._run(e.spawn),i}}},{"./errors":12,"./util":36}],17:[function(t,e,n){"use strict";e.exports=function(e,n,r,i,o){var s=t("./util");s.canEvaluate,s.tryCatch,s.errorObj,e.join=function(){var t,e=arguments.length-1;e>0&&"function"==typeof arguments[e]&&(t=arguments[e]);var r=[].slice.call(arguments);t&&r.pop();var i=new n(r).promise();return void 0!==t?i.spread(t):i}}},{"./util":36}],18:[function(t,e,n){"use strict";e.exports=function(e,n,r,i,o,s){function a(t,n,r,i){this.constructor$(t),this._promise._captureStackTrace();var s=e._getContext();if(this._callback=l.contextBind(s,n),this._preservedValues=i===o?new Array(this.length()):null,this._limit=r,this._inFlight=0,this._queue=[],f.invoke(this._asyncInit,this,void 0),l.isArray(t))for(var a=0;a<t.length;++a){var c=t[a];c instanceof e&&c.suppressUnhandledRejections()}}function c(t,n,i,o){if("function"!=typeof n)return r("expecting a function but got "+l.classString(n));var s=0;if(void 0!==i){if("object"!=typeof i||null===i)return e.reject(new TypeError("options argument must be an object but it is "+l.classString(i)));if("number"!=typeof i.concurrency)return e.reject(new TypeError("'concurrency' must be a number but it is "+l.classString(i.concurrency)));s=i.concurrency}return new a(t,n,s="number"==typeof s&&isFinite(s)&&s>=1?s:0,o).promise()}var l=t("./util"),u=l.tryCatch,p=l.errorObj,f=e._async;l.inherits(a,n),a.prototype._asyncInit=function(){this._init$(void 0,-2)},a.prototype._init=function(){},a.prototype._promiseFulfilled=function(t,n){var r=this._values,o=this.length(),a=this._preservedValues,c=this._limit;if(0>n){if(r[n=-1*n-1]=t,c>=1&&(this._inFlight--,this._drainQueue(),this._isResolved()))return!0}else{if(c>=1&&this._inFlight>=c)return r[n]=t,this._queue.push(n),!1;null!==a&&(a[n]=t);var l=this._promise,f=this._callback,h=l._boundValue();l._pushContext();var _=u(f).call(h,t,n,o),d=l._popContext();if(s.checkForgottenReturns(_,d,null!==a?"Promise.filter":"Promise.map",l),_===p)return this._reject(_.e),!0;var v=i(_,this._promise);if(v instanceof e){var y=(v=v._target())._bitField;if(0==(50397184&y))return c>=1&&this._inFlight++,r[n]=v,v._proxy(this,-1*(n+1)),!1;if(0==(33554432&y))return 0!=(16777216&y)?(this._reject(v._reason()),!0):(this._cancel(),!0);_=v._value()}r[n]=_}return++this._totalResolved>=o&&(null!==a?this._filter(r,a):this._resolve(r),!0)},a.prototype._drainQueue=function(){for(var t=this._queue,e=this._limit,n=this._values;t.length>0&&this._inFlight<e;){if(this._isResolved())return;var r=t.pop();this._promiseFulfilled(n[r],r)}},a.prototype._filter=function(t,e){for(var n=e.length,r=new Array(n),i=0,o=0;n>o;++o)t[o]&&(r[i++]=e[o]);r.length=i,this._resolve(r)},a.prototype.preservedValues=function(){return this._preservedValues},e.prototype.map=function(t,e){return c(this,t,e,null)},e.map=function(t,e,n,r){return c(t,e,n,r)}}},{"./util":36}],19:[function(t,e,n){"use strict";e.exports=function(e,n,r,i,o){var s=t("./util"),a=s.tryCatch;e.method=function(t){if("function"!=typeof t)throw new e.TypeError("expecting a function but got "+s.classString(t));return function(){var r=new e(n);r._captureStackTrace(),r._pushContext();var i=a(t).apply(this,arguments),s=r._popContext();return o.checkForgottenReturns(i,s,"Promise.method",r),r._resolveFromSyncValue(i),r}},e.attempt=e.try=function(t){if("function"!=typeof t)return i("expecting a function but got "+s.classString(t));var r,c=new e(n);if(c._captureStackTrace(),c._pushContext(),arguments.length>1){o.deprecated("calling Promise.try with more than 1 argument");var l=arguments[1],u=arguments[2];r=s.isArray(l)?a(t).apply(u,l):a(t).call(u,l)}else r=a(t)();var p=c._popContext();return o.checkForgottenReturns(r,p,"Promise.try",c),c._resolveFromSyncValue(r),c},e.prototype._resolveFromSyncValue=function(t){t===s.errorObj?this._rejectCallback(t.e,!1):this._resolveCallback(t,!0)}}},{"./util":36}],20:[function(t,e,n){"use strict";function r(t){var e;if(function(t){return t instanceof Error&&a.getPrototypeOf(t)===Error.prototype}(t)){(e=new s(t)).name=t.name,e.message=t.message,e.stack=t.stack;for(var n=a.keys(t),r=0;r<n.length;++r){var o=n[r];c.test(o)||(e[o]=t[o])}return e}return i.markAsOriginatingFromRejection(t),t}var i=t("./util"),o=i.maybeWrapAsError,s=t("./errors").OperationalError,a=t("./es5"),c=/^(?:name|message|stack|cause)$/;e.exports=function(t,e){return function(n,i){if(null!==t){if(n){var s=r(o(n));t._attachExtraTrace(s),t._reject(s)}else if(e){var a=[].slice.call(arguments,1);t._fulfill(a)}else t._fulfill(i);t=null}}}},{"./errors":12,"./es5":13,"./util":36}],21:[function(t,e,n){"use strict";e.exports=function(e){function n(t,e){if(!o.isArray(t))return r.call(this,t,e);var n=a(e).apply(this._boundValue(),[null].concat(t));n===c&&s.throwLater(n.e)}function r(t,e){var n=this._boundValue(),r=void 0===t?a(e).call(n,null):a(e).call(n,null,t);r===c&&s.throwLater(r.e)}function i(t,e){if(!t){var n=new Error(t+"");n.cause=t,t=n}var r=a(e).call(this._boundValue(),t);r===c&&s.throwLater(r.e)}var o=t("./util"),s=e._async,a=o.tryCatch,c=o.errorObj;e.prototype.asCallback=e.prototype.nodeify=function(t,e){if("function"==typeof t){var o=r;void 0!==e&&Object(e).spread&&(o=n),this._then(o,i,void 0,this,t)}return this}}},{"./util":36}],22:[function(t,e,n){"use strict";e.exports=function(){function n(){}function r(t){t!==j&&function(t,e){if(null==t||t.constructor!==r)throw new w("the promise constructor cannot be invoked directly\n\n    See http://goo.gl/MqrFmX\n");if("function"!=typeof e)throw new w("expecting a function but got "+p.classString(e))}(this,t),this._bitField=0,this._fulfillmentHandler0=void 0,this._rejectionHandler0=void 0,this._promise0=void 0,this._receiver0=void 0,this._resolveFromExecutor(t),this._promiseCreated(),this._fireEvent("promiseCreated",this)}function i(t){this.promise._resolveCallback(t)}function o(t){this.promise._rejectCallback(t,!1)}function s(t){var e=new r(j);e._fulfillmentHandler0=t,e._rejectionHandler0=t,e._promise0=t,e._receiver0=t}var a=function(){return new w("circular promise resolution chain\n\n    See http://goo.gl/MqrFmX\n")},c=function(){return new r.PromiseInspection(this._target())},l=function(t){return r.reject(new w(t))},u={},p=t("./util");p.setReflectHandler(c);var f=function(){var t=process.domain;return void 0===t?null:t},h=function(){return{domain:f(),async:null}},_=p.isNode&&p.nodeSupportsAsyncResource?t("async_hooks").AsyncResource:null,d=function(){return{domain:f(),async:new _("Bluebird::Promise")}},v=p.isNode?h:function(){return null};p.notEnumerableProp(r,"_getContext",v);var y=t("./es5"),g=t("./async"),m=new g;y.defineProperty(r,"_async",{value:m});var b=t("./errors"),w=r.TypeError=b.TypeError;r.RangeError=b.RangeError;var C=r.CancellationError=b.CancellationError;r.TimeoutError=b.TimeoutError,r.OperationalError=b.OperationalError,r.RejectionError=b.OperationalError,r.AggregateError=b.AggregateError;var j=function(){},k={},E={},F=t("./thenables")(r,j),x=t("./promise_array")(r,j,F,l,n),T=t("./context")(r),P=T.create,R=t("./debuggability")(r,T,(function(){v=d,p.notEnumerableProp(r,"_getContext",d)}),(function(){v=h,p.notEnumerableProp(r,"_getContext",h)})),S=(R.CapturedTrace,t("./finally")(r,F,E)),O=t("./catch_filter")(E),A=t("./nodeback"),V=p.errorObj,H=p.tryCatch;return r.prototype.toString=function(){return"[object Promise]"},r.prototype.caught=r.prototype.catch=function(t){var e=arguments.length;if(e>1){var n,r=new Array(e-1),i=0;for(n=0;e-1>n;++n){var o=arguments[n];if(!p.isObject(o))return l("Catch statement predicate: expecting an object but got "+p.classString(o));r[i++]=o}if(r.length=i,"function"!=typeof(t=arguments[n]))throw new w("The last argument to .catch() must be a function, got "+p.toString(t));return this.then(void 0,O(r,t,this))}return this.then(void 0,t)},r.prototype.reflect=function(){return this._then(c,c,void 0,this,void 0)},r.prototype.then=function(t,e){if(R.warnings()&&arguments.length>0&&"function"!=typeof t&&"function"!=typeof e){var n=".then() only accepts functions but was passed: "+p.classString(t);arguments.length>1&&(n+=", "+p.classString(e)),this._warn(n)}return this._then(t,e,void 0,void 0,void 0)},r.prototype.done=function(t,e){this._then(t,e,void 0,void 0,void 0)._setIsFinal()},r.prototype.spread=function(t){return"function"!=typeof t?l("expecting a function but got "+p.classString(t)):this.all()._then(t,void 0,void 0,k,void 0)},r.prototype.toJSON=function(){var t={isFulfilled:!1,isRejected:!1,fulfillmentValue:void 0,rejectionReason:void 0};return this.isFulfilled()?(t.fulfillmentValue=this.value(),t.isFulfilled=!0):this.isRejected()&&(t.rejectionReason=this.reason(),t.isRejected=!0),t},r.prototype.all=function(){return arguments.length>0&&this._warn(".all() was passed arguments but it does not take any"),new x(this).promise()},r.prototype.error=function(t){return this.caught(p.originatesFromRejection,t)},r.getNewLibraryCopy=e.exports,r.is=function(t){return t instanceof r},r.fromNode=r.fromCallback=function(t){var e=new r(j);e._captureStackTrace();var n=arguments.length>1&&!!Object(arguments[1]).multiArgs,i=H(t)(A(e,n));return i===V&&e._rejectCallback(i.e,!0),e._isFateSealed()||e._setAsyncGuaranteed(),e},r.all=function(t){return new x(t).promise()},r.cast=function(t){var e=F(t);return e instanceof r||((e=new r(j))._captureStackTrace(),e._setFulfilled(),e._rejectionHandler0=t),e},r.resolve=r.fulfilled=r.cast,r.reject=r.rejected=function(t){var e=new r(j);return e._captureStackTrace(),e._rejectCallback(t,!0),e},r.setScheduler=function(t){if("function"!=typeof t)throw new w("expecting a function but got "+p.classString(t));return m.setScheduler(t)},r.prototype._then=function(t,e,n,i,o){var s=void 0!==o,a=s?o:new r(j),c=this._target(),l=c._bitField;s||(a._propagateFrom(this,3),a._captureStackTrace(),void 0===i&&0!=(2097152&this._bitField)&&(i=0!=(50397184&l)?this._boundValue():c===this?void 0:this._boundTo),this._fireEvent("promiseChained",this,a));var u=v();if(0!=(50397184&l)){var f,h,_=c._settlePromiseCtx;0!=(33554432&l)?(h=c._rejectionHandler0,f=t):0!=(16777216&l)?(h=c._fulfillmentHandler0,f=e,c._unsetRejectionIsUnhandled()):(_=c._settlePromiseLateCancellationObserver,h=new C("late cancellation observer"),c._attachExtraTrace(h),f=e),m.invoke(_,c,{handler:p.contextBind(u,f),promise:a,receiver:i,value:h})}else c._addCallbacks(t,e,a,i,u);return a},r.prototype._length=function(){return 65535&this._bitField},r.prototype._isFateSealed=function(){return 0!=(117506048&this._bitField)},r.prototype._isFollowing=function(){return 67108864==(67108864&this._bitField)},r.prototype._setLength=function(t){this._bitField=-65536&this._bitField|65535&t},r.prototype._setFulfilled=function(){this._bitField=33554432|this._bitField,this._fireEvent("promiseFulfilled",this)},r.prototype._setRejected=function(){this._bitField=16777216|this._bitField,this._fireEvent("promiseRejected",this)},r.prototype._setFollowing=function(){this._bitField=67108864|this._bitField,this._fireEvent("promiseResolved",this)},r.prototype._setIsFinal=function(){this._bitField=4194304|this._bitField},r.prototype._isFinal=function(){return(4194304&this._bitField)>0},r.prototype._unsetCancelled=function(){this._bitField=-65537&this._bitField},r.prototype._setCancelled=function(){this._bitField=65536|this._bitField,this._fireEvent("promiseCancelled",this)},r.prototype._setWillBeCancelled=function(){this._bitField=8388608|this._bitField},r.prototype._setAsyncGuaranteed=function(){if(!m.hasCustomScheduler()){var t=this._bitField;this._bitField=t|(536870912&t)>>2^134217728}},r.prototype._setNoAsyncGuarantee=function(){this._bitField=-134217729&(536870912|this._bitField)},r.prototype._receiverAt=function(t){var e=0===t?this._receiver0:this[4*t-4+3];return e===u?void 0:void 0===e&&this._isBound()?this._boundValue():e},r.prototype._promiseAt=function(t){return this[4*t-4+2]},r.prototype._fulfillmentHandlerAt=function(t){return this[4*t-4+0]},r.prototype._rejectionHandlerAt=function(t){return this[4*t-4+1]},r.prototype._boundValue=function(){},r.prototype._migrateCallback0=function(t){var e=(t._bitField,t._fulfillmentHandler0),n=t._rejectionHandler0,r=t._promise0,i=t._receiverAt(0);void 0===i&&(i=u),this._addCallbacks(e,n,r,i,null)},r.prototype._migrateCallbackAt=function(t,e){var n=t._fulfillmentHandlerAt(e),r=t._rejectionHandlerAt(e),i=t._promiseAt(e),o=t._receiverAt(e);void 0===o&&(o=u),this._addCallbacks(n,r,i,o,null)},r.prototype._addCallbacks=function(t,e,n,r,i){var o=this._length();if(o>=65531&&(o=0,this._setLength(0)),0===o)this._promise0=n,this._receiver0=r,"function"==typeof t&&(this._fulfillmentHandler0=p.contextBind(i,t)),"function"==typeof e&&(this._rejectionHandler0=p.contextBind(i,e));else{var s=4*o-4;this[s+2]=n,this[s+3]=r,"function"==typeof t&&(this[s+0]=p.contextBind(i,t)),"function"==typeof e&&(this[s+1]=p.contextBind(i,e))}return this._setLength(o+1),o},r.prototype._proxy=function(t,e){this._addCallbacks(void 0,void 0,e,t,null)},r.prototype._resolveCallback=function(t,e){if(0==(117506048&this._bitField)){if(t===this)return this._rejectCallback(a(),!1);var n=F(t,this);if(!(n instanceof r))return this._fulfill(t);e&&this._propagateFrom(n,2);var i=n._target();if(i===this)return void this._reject(a());var o=i._bitField;if(0==(50397184&o)){var s=this._length();s>0&&i._migrateCallback0(this);for(var c=1;s>c;++c)i._migrateCallbackAt(this,c);this._setFollowing(),this._setLength(0),this._setFollowee(n)}else if(0!=(33554432&o))this._fulfill(i._value());else if(0!=(16777216&o))this._reject(i._reason());else{var l=new C("late cancellation observer");i._attachExtraTrace(l),this._reject(l)}}},r.prototype._rejectCallback=function(t,e,n){var r=p.ensureErrorObject(t),i=r===t;if(!i&&!n&&R.warnings()){var o="a promise was rejected with a non-error: "+p.classString(t);this._warn(o,!0)}this._attachExtraTrace(r,!!e&&i),this._reject(t)},r.prototype._resolveFromExecutor=function(t){if(t!==j){var e=this;this._captureStackTrace(),this._pushContext();var n=!0,r=this._execute(t,(function(t){e._resolveCallback(t)}),(function(t){e._rejectCallback(t,n)}));n=!1,this._popContext(),void 0!==r&&e._rejectCallback(r,!0)}},r.prototype._settlePromiseFromHandler=function(t,e,n,r){var i=r._bitField;if(0==(65536&i)){var o;r._pushContext(),e===k?n&&"number"==typeof n.length?o=H(t).apply(this._boundValue(),n):(o=V).e=new w("cannot .spread() a non-array: "+p.classString(n)):o=H(t).call(e,n);var s=r._popContext();0==(65536&(i=r._bitField))&&(o===E?r._reject(n):o===V?r._rejectCallback(o.e,!1):(R.checkForgottenReturns(o,s,"",r,this),r._resolveCallback(o)))}},r.prototype._target=function(){for(var t=this;t._isFollowing();)t=t._followee();return t},r.prototype._followee=function(){return this._rejectionHandler0},r.prototype._setFollowee=function(t){this._rejectionHandler0=t},r.prototype._settlePromise=function(t,e,i,o){var s=t instanceof r,a=this._bitField,l=0!=(134217728&a);0!=(65536&a)?(s&&t._invokeInternalOnCancel(),i instanceof S&&i.isFinallyHandler()?(i.cancelPromise=t,H(e).call(i,o)===V&&t._reject(V.e)):e===c?t._fulfill(c.call(i)):i instanceof n?i._promiseCancelled(t):s||t instanceof x?t._cancel():i.cancel()):"function"==typeof e?s?(l&&t._setAsyncGuaranteed(),this._settlePromiseFromHandler(e,i,o,t)):e.call(i,o,t):i instanceof n?i._isResolved()||(0!=(33554432&a)?i._promiseFulfilled(o,t):i._promiseRejected(o,t)):s&&(l&&t._setAsyncGuaranteed(),0!=(33554432&a)?t._fulfill(o):t._reject(o))},r.prototype._settlePromiseLateCancellationObserver=function(t){var e=t.handler,n=t.promise,i=t.receiver,o=t.value;"function"==typeof e?n instanceof r?this._settlePromiseFromHandler(e,i,o,n):e.call(i,o,n):n instanceof r&&n._reject(o)},r.prototype._settlePromiseCtx=function(t){this._settlePromise(t.promise,t.handler,t.receiver,t.value)},r.prototype._settlePromise0=function(t,e,n){var r=this._promise0,i=this._receiverAt(0);this._promise0=void 0,this._receiver0=void 0,this._settlePromise(r,t,i,e)},r.prototype._clearCallbackDataAtIndex=function(t){var e=4*t-4;this[e+2]=this[e+3]=this[e+0]=this[e+1]=void 0},r.prototype._fulfill=function(t){var e=this._bitField;if(!((117506048&e)>>>16)){if(t===this){var n=a();return this._attachExtraTrace(n),this._reject(n)}this._setFulfilled(),this._rejectionHandler0=t,(65535&e)>0&&(0!=(134217728&e)?this._settlePromises():m.settlePromises(this),this._dereferenceTrace())}},r.prototype._reject=function(t){var e=this._bitField;if(!((117506048&e)>>>16))return this._setRejected(),this._fulfillmentHandler0=t,this._isFinal()?m.fatalError(t,p.isNode):void((65535&e)>0?m.settlePromises(this):this._ensurePossibleRejectionHandled())},r.prototype._fulfillPromises=function(t,e){for(var n=1;t>n;n++){var r=this._fulfillmentHandlerAt(n),i=this._promiseAt(n),o=this._receiverAt(n);this._clearCallbackDataAtIndex(n),this._settlePromise(i,r,o,e)}},r.prototype._rejectPromises=function(t,e){for(var n=1;t>n;n++){var r=this._rejectionHandlerAt(n),i=this._promiseAt(n),o=this._receiverAt(n);this._clearCallbackDataAtIndex(n),this._settlePromise(i,r,o,e)}},r.prototype._settlePromises=function(){var t=this._bitField,e=65535&t;if(e>0){if(0!=(16842752&t)){var n=this._fulfillmentHandler0;this._settlePromise0(this._rejectionHandler0,n,t),this._rejectPromises(e,n)}else{var r=this._rejectionHandler0;this._settlePromise0(this._fulfillmentHandler0,r,t),this._fulfillPromises(e,r)}this._setLength(0)}this._clearCancellationData()},r.prototype._settledValue=function(){var t=this._bitField;return 0!=(33554432&t)?this._rejectionHandler0:0!=(16777216&t)?this._fulfillmentHandler0:void 0},"undefined"!=typeof Symbol&&Symbol.toStringTag&&y.defineProperty(r.prototype,Symbol.toStringTag,{get:function(){return"Object"}}),r.defer=r.pending=function(){return R.deprecated("Promise.defer","new Promise"),{promise:new r(j),resolve:i,reject:o}},p.notEnumerableProp(r,"_makeSelfResolutionError",a),t("./method")(r,j,F,l,R),t("./bind")(r,j,F,R),t("./cancel")(r,x,l,R),t("./direct_resolve")(r),t("./synchronous_inspection")(r),t("./join")(r,x,F,j,m),r.Promise=r,r.version="3.7.2",t("./call_get.js")(r),t("./generators.js")(r,l,j,F,n,R),t("./map.js")(r,x,l,F,j,R),t("./nodeify.js")(r),t("./promisify.js")(r,j),t("./props.js")(r,x,F,l),t("./race.js")(r,j,F,l),t("./reduce.js")(r,x,l,F,j,R),t("./settle.js")(r,x,R),t("./some.js")(r,x,l),t("./timers.js")(r,j,R),t("./using.js")(r,l,F,P,j,R),t("./any.js")(r),t("./each.js")(r,j),t("./filter.js")(r,j),p.toFastProperties(r),p.toFastProperties(r.prototype),s({a:1}),s({b:2}),s({c:3}),s(1),s((function(){})),s(void 0),s(!1),s(new r(j)),R.setBounds(g.firstLineError,p.lastLineError),r}},{"./any.js":1,"./async":2,"./bind":3,"./call_get.js":5,"./cancel":6,"./catch_filter":7,"./context":8,"./debuggability":9,"./direct_resolve":10,"./each.js":11,"./errors":12,"./es5":13,"./filter.js":14,"./finally":15,"./generators.js":16,"./join":17,"./map.js":18,"./method":19,"./nodeback":20,"./nodeify.js":21,"./promise_array":23,"./promisify.js":24,"./props.js":25,"./race.js":27,"./reduce.js":28,"./settle.js":30,"./some.js":31,"./synchronous_inspection":32,"./thenables":33,"./timers.js":34,"./using.js":35,"./util":36,async_hooks:void 0}],23:[function(t,e,n){"use strict";e.exports=function(e,n,r,i,o){function s(t){var r=this._promise=new e(n);t instanceof e&&(r._propagateFrom(t,3),t.suppressUnhandledRejections()),r._setOnCancel(this),this._values=t,this._length=0,this._totalResolved=0,this._init(void 0,-2)}var a=t("./util");return a.isArray,a.inherits(s,o),s.prototype.length=function(){return this._length},s.prototype.promise=function(){return this._promise},s.prototype._init=function t(n,o){var s=r(this._values,this._promise);if(s instanceof e){var c=(s=s._target())._bitField;if(this._values=s,0==(50397184&c))return this._promise._setAsyncGuaranteed(),s._then(t,this._reject,void 0,this,o);if(0==(33554432&c))return 0!=(16777216&c)?this._reject(s._reason()):this._cancel();s=s._value()}if(null!==(s=a.asArray(s)))return 0===s.length?void(-5===o?this._resolveEmptyArray():this._resolve(function(t){switch(t){case-2:return[];case-3:return{};case-6:return new Map}}(o))):void this._iterate(s);var l=i("expecting an array or an iterable object but got "+a.classString(s)).reason();this._promise._rejectCallback(l,!1)},s.prototype._iterate=function(t){var n=this.getActualLength(t.length);this._length=n,this._values=this.shouldCopyValues()?new Array(n):this._values;for(var i=this._promise,o=!1,s=null,a=0;n>a;++a){var c=r(t[a],i);c instanceof e?s=(c=c._target())._bitField:s=null,o?null!==s&&c.suppressUnhandledRejections():null!==s?0==(50397184&s)?(c._proxy(this,a),this._values[a]=c):o=0!=(33554432&s)?this._promiseFulfilled(c._value(),a):0!=(16777216&s)?this._promiseRejected(c._reason(),a):this._promiseCancelled(a):o=this._promiseFulfilled(c,a)}o||i._setAsyncGuaranteed()},s.prototype._isResolved=function(){return null===this._values},s.prototype._resolve=function(t){this._values=null,this._promise._fulfill(t)},s.prototype._cancel=function(){!this._isResolved()&&this._promise._isCancellable()&&(this._values=null,this._promise._cancel())},s.prototype._reject=function(t){this._values=null,this._promise._rejectCallback(t,!1)},s.prototype._promiseFulfilled=function(t,e){return this._values[e]=t,++this._totalResolved>=this._length&&(this._resolve(this._values),!0)},s.prototype._promiseCancelled=function(){return this._cancel(),!0},s.prototype._promiseRejected=function(t){return this._totalResolved++,this._reject(t),!0},s.prototype._resultCancelled=function(){if(!this._isResolved()){var t=this._values;if(this._cancel(),t instanceof e)t.cancel();else for(var n=0;n<t.length;++n)t[n]instanceof e&&t[n].cancel()}},s.prototype.shouldCopyValues=function(){return!0},s.prototype.getActualLength=function(t){return t},s}},{"./util":36}],24:[function(t,e,n){"use strict";e.exports=function(e,n){function r(t){return!v.test(t)}function i(t){try{return!0===t.__isPromisified__}catch(t){return!1}}function o(t,e,n){var r=l.getDataPropertyOrDefault(t,e+n,d);return!!r&&i(r)}function s(t,e,n,r){for(var s=l.inheritedDataKeys(t),a=[],c=0;c<s.length;++c){var u=s[c],p=t[u],f=r===y||y(u,p,t);"function"!=typeof p||i(p)||o(t,u,e)||!r(u,p,t,f)||a.push(u,p)}return function(t,e,n){for(var r=0;r<t.length;r+=2){var i=t[r];if(n.test(i))for(var o=i.replace(n,""),s=0;s<t.length;s+=2)if(t[s]===o)throw new _("Cannot promisify an API that has normal methods with '%s'-suffix\n\n    See http://goo.gl/MqrFmX\n".replace("%s",e))}}(a,e,n),a}function a(t,e,n,r,i){for(var o=new RegExp(g(e)+"$"),a=s(t,e,o,n),u=0,p=a.length;p>u;u+=2){var f=a[u],h=a[u+1],_=f+e;if(r===m)t[_]=m(f,c,f,h,e,i);else{var d=r(h,(function(){return m(f,c,f,h,e,i)}));l.notEnumerableProp(d,"__isPromisified__",!0),t[_]=d}}return l.toFastProperties(t),t}var c={},l=t("./util"),u=t("./nodeback"),p=l.withAppended,f=l.maybeWrapAsError,h=l.canEvaluate,_=t("./errors").TypeError,d={__isPromisified__:!0},v=new RegExp("^(?:"+["arity","length","name","arguments","caller","callee","prototype","__isPromisified__"].join("|")+")$"),y=function(t){return l.isIdentifier(t)&&"_"!==t.charAt(0)&&"constructor"!==t},g=function(t){return t.replace(/([$])/,"\\$")},m=h?undefined:function(t,r,i,o,s,a){function h(){var i=r;r===c&&(i=this);var o=new e(n);o._captureStackTrace();var s="string"==typeof d&&this!==_?this[d]:t,l=u(o,a);try{s.apply(i,p(arguments,l))}catch(t){o._rejectCallback(f(t),!0,!0)}return o._isFateSealed()||o._setAsyncGuaranteed(),o}var _=function(){return this}(),d=t;return"string"==typeof d&&(t=o),l.notEnumerableProp(h,"__isPromisified__",!0),h};e.promisify=function(t,e){if("function"!=typeof t)throw new _("expecting a function but got "+l.classString(t));if(i(t))return t;var n=function(t,e,n){return m(t,e,void 0,t,null,n)}(t,void 0===(e=Object(e)).context?c:e.context,!!e.multiArgs);return l.copyDescriptors(t,n,r),n},e.promisifyAll=function(t,e){if("function"!=typeof t&&"object"!=typeof t)throw new _("the target of promisifyAll must be an object or a function\n\n    See http://goo.gl/MqrFmX\n");var n=!!(e=Object(e)).multiArgs,r=e.suffix;"string"!=typeof r&&(r="Async");var i=e.filter;"function"!=typeof i&&(i=y);var o=e.promisifier;if("function"!=typeof o&&(o=m),!l.isIdentifier(r))throw new RangeError("suffix must be a valid identifier\n\n    See http://goo.gl/MqrFmX\n");for(var s=l.inheritedDataKeys(t),c=0;c<s.length;++c){var u=t[s[c]];"constructor"!==s[c]&&l.isClass(u)&&(a(u.prototype,r,i,o,n),a(u,r,i,o,n))}return a(t,r,i,o,n)}}},{"./errors":12,"./nodeback":20,"./util":36}],25:[function(t,e,n){"use strict";e.exports=function(e,n,r,i){function o(t){var e,n=!1;if(void 0!==a&&t instanceof a)e=p(t),n=!0;else{var r=u.keys(t),i=r.length;e=new Array(2*i);for(var o=0;i>o;++o){var s=r[o];e[o]=t[s],e[o+i]=s}}this.constructor$(e),this._isMap=n,this._init$(void 0,n?-6:-3)}function s(t){var n,s=r(t);return l(s)?(n=s instanceof e?s._then(e.props,void 0,void 0,void 0,void 0):new o(s).promise(),s instanceof e&&n._propagateFrom(s,2),n):i("cannot await properties of a non-object\n\n    See http://goo.gl/MqrFmX\n")}var a,c=t("./util"),l=c.isObject,u=t("./es5");"function"==typeof Map&&(a=Map);var p=function(){function t(t,r){this[e]=t,this[e+n]=r,e++}var e=0,n=0;return function(r){n=r.size,e=0;var i=new Array(2*r.size);return r.forEach(t,i),i}}();c.inherits(o,n),o.prototype._init=function(){},o.prototype._promiseFulfilled=function(t,e){if(this._values[e]=t,++this._totalResolved>=this._length){var n;if(this._isMap)n=function(t){for(var e=new a,n=t.length/2|0,r=0;n>r;++r){var i=t[n+r],o=t[r];e.set(i,o)}return e}(this._values);else{n={};for(var r=this.length(),i=0,o=this.length();o>i;++i)n[this._values[i+r]]=this._values[i]}return this._resolve(n),!0}return!1},o.prototype.shouldCopyValues=function(){return!1},o.prototype.getActualLength=function(t){return t>>1},e.prototype.props=function(){return s(this)},e.props=function(t){return s(t)}}},{"./es5":13,"./util":36}],26:[function(t,e,n){"use strict";function r(t){this._capacity=t,this._length=0,this._front=0}r.prototype._willBeOverCapacity=function(t){return this._capacity<t},r.prototype._pushOne=function(t){var e=this.length();this._checkCapacity(e+1),this[this._front+e&this._capacity-1]=t,this._length=e+1},r.prototype.push=function(t,e,n){var r=this.length()+3;if(this._willBeOverCapacity(r))return this._pushOne(t),this._pushOne(e),void this._pushOne(n);var i=this._front+r-3;this._checkCapacity(r);var o=this._capacity-1;this[i+0&o]=t,this[i+1&o]=e,this[i+2&o]=n,this._length=r},r.prototype.shift=function(){var t=this._front,e=this[t];return this[t]=void 0,this._front=t+1&this._capacity-1,this._length--,e},r.prototype.length=function(){return this._length},r.prototype._checkCapacity=function(t){this._capacity<t&&this._resizeTo(this._capacity<<1)},r.prototype._resizeTo=function(t){var e=this._capacity;this._capacity=t,function(t,e,n,r,i){for(var o=0;i>o;++o)n[o+r]=t[o+e],t[o+e]=void 0}(this,0,this,e,this._front+this._length&e-1)},e.exports=r},{}],27:[function(t,e,n){"use strict";e.exports=function(e,n,r,i){function o(t,o){var c=r(t);if(c instanceof e)return a(c);if(null===(t=s.asArray(t)))return i("expecting an array or an iterable object but got "+s.classString(t));var l=new e(n);void 0!==o&&l._propagateFrom(o,3);for(var u=l._fulfill,p=l._reject,f=0,h=t.length;h>f;++f){var _=t[f];(void 0!==_||f in t)&&e.cast(_)._then(u,p,void 0,l,null)}return l}var s=t("./util"),a=function(t){return t.then((function(e){return o(e,t)}))};e.race=function(t){return o(t,void 0)},e.prototype.race=function(){return o(this,void 0)}}},{"./util":36}],28:[function(t,e,n){"use strict";e.exports=function(e,n,r,i,o,s){function a(t,n,r,i){this.constructor$(t);var s=e._getContext();this._fn=f.contextBind(s,n),void 0!==r&&(r=e.resolve(r))._attachCancellationCallback(this),this._initialValue=r,this._currentCancellable=null,this._eachValues=i===o?Array(this._length):0===i?null:void 0,this._promise._captureStackTrace(),this._init$(void 0,-5)}function c(t,e){this.isFulfilled()?e._resolve(t):e._reject(t)}function l(t,e,n,i){return"function"!=typeof e?r("expecting a function but got "+f.classString(e)):new a(t,e,n,i).promise()}function u(t){this.accum=t,this.array._gotAccum(t);var n=i(this.value,this.array._promise);return n instanceof e?(this.array._currentCancellable=n,n._then(p,void 0,void 0,this,void 0)):p.call(this,n)}function p(t){var n,r=this.array,i=r._promise,o=h(r._fn);i._pushContext(),(n=void 0!==r._eachValues?o.call(i._boundValue(),t,this.index,this.length):o.call(i._boundValue(),this.accum,t,this.index,this.length))instanceof e&&(r._currentCancellable=n);var a=i._popContext();return s.checkForgottenReturns(n,a,void 0!==r._eachValues?"Promise.each":"Promise.reduce",i),n}var f=t("./util"),h=f.tryCatch;f.inherits(a,n),a.prototype._gotAccum=function(t){void 0!==this._eachValues&&null!==this._eachValues&&t!==o&&this._eachValues.push(t)},a.prototype._eachComplete=function(t){return null!==this._eachValues&&this._eachValues.push(t),this._eachValues},a.prototype._init=function(){},a.prototype._resolveEmptyArray=function(){this._resolve(void 0!==this._eachValues?this._eachValues:this._initialValue)},a.prototype.shouldCopyValues=function(){return!1},a.prototype._resolve=function(t){this._promise._resolveCallback(t),this._values=null},a.prototype._resultCancelled=function(t){return t===this._initialValue?this._cancel():void(this._isResolved()||(this._resultCancelled$(),this._currentCancellable instanceof e&&this._currentCancellable.cancel(),this._initialValue instanceof e&&this._initialValue.cancel()))},a.prototype._iterate=function(t){this._values=t;var n,r,i=t.length;void 0!==this._initialValue?(n=this._initialValue,r=0):(n=e.resolve(t[0]),r=1),this._currentCancellable=n;for(var o=r;i>o;++o){var s=t[o];s instanceof e&&s.suppressUnhandledRejections()}if(!n.isRejected())for(;i>r;++r){var a={accum:null,value:t[r],index:r,length:i,array:this};n=n._then(u,void 0,void 0,a,void 0),0==(127&r)&&n._setNoAsyncGuarantee()}void 0!==this._eachValues&&(n=n._then(this._eachComplete,void 0,void 0,this,void 0)),n._then(c,c,void 0,n,this)},e.prototype.reduce=function(t,e){return l(this,t,e,null)},e.reduce=function(t,e,n,r){return l(t,e,n,r)}}},{"./util":36}],29:[function(t,e,n){"use strict";var r,i=t("./util"),o=i.getNativePromise();if(i.isNode&&"undefined"==typeof MutationObserver){var s=global.setImmediate,a=process.nextTick;r=i.isRecentNode?function(t){s.call(global,t)}:function(t){a.call(process,t)}}else if("function"==typeof o&&"function"==typeof o.resolve){var c=o.resolve();r=function(t){c.then(t)}}else r="undefined"==typeof MutationObserver||"undefined"!=typeof window&&window.navigator&&(window.navigator.standalone||window.cordova)||!("classList"in document.documentElement)?"undefined"!=typeof setImmediate?function(t){setImmediate(t)}:"undefined"!=typeof setTimeout?function(t){setTimeout(t,0)}:function(){throw new Error("No async scheduler available\n\n    See http://goo.gl/MqrFmX\n")}:function(){var t=document.createElement("div"),e={attributes:!0},n=!1,r=document.createElement("div");new MutationObserver((function(){t.classList.toggle("foo"),n=!1})).observe(r,e);var i=function(){n||(n=!0,r.classList.toggle("foo"))};return function(n){var r=new MutationObserver((function(){r.disconnect(),n()}));r.observe(t,e),i()}}();e.exports=r},{"./util":36}],30:[function(t,e,n){"use strict";e.exports=function(e,n,r){function i(t){this.constructor$(t)}var o=e.PromiseInspection;t("./util").inherits(i,n),i.prototype._promiseResolved=function(t,e){return this._values[t]=e,++this._totalResolved>=this._length&&(this._resolve(this._values),!0)},i.prototype._promiseFulfilled=function(t,e){var n=new o;return n._bitField=33554432,n._settledValueField=t,this._promiseResolved(e,n)},i.prototype._promiseRejected=function(t,e){var n=new o;return n._bitField=16777216,n._settledValueField=t,this._promiseResolved(e,n)},e.settle=function(t){return r.deprecated(".settle()",".reflect()"),new i(t).promise()},e.allSettled=function(t){return new i(t).promise()},e.prototype.settle=function(){return e.settle(this)}}},{"./util":36}],31:[function(t,e,n){"use strict";e.exports=function(e,n,r){function i(t){this.constructor$(t),this._howMany=0,this._unwrap=!1,this._initialized=!1}function o(t,e){if((0|e)!==e||0>e)return r("expecting a positive integer\n\n    See http://goo.gl/MqrFmX\n");var n=new i(t),o=n.promise();return n.setHowMany(e),n.init(),o}var s=t("./util"),a=t("./errors").RangeError,c=t("./errors").AggregateError,l=s.isArray,u={};s.inherits(i,n),i.prototype._init=function(){if(this._initialized){if(0===this._howMany)return void this._resolve([]);this._init$(void 0,-5);var t=l(this._values);!this._isResolved()&&t&&this._howMany>this._canPossiblyFulfill()&&this._reject(this._getRangeError(this.length()))}},i.prototype.init=function(){this._initialized=!0,this._init()},i.prototype.setUnwrap=function(){this._unwrap=!0},i.prototype.howMany=function(){return this._howMany},i.prototype.setHowMany=function(t){this._howMany=t},i.prototype._promiseFulfilled=function(t){return this._addFulfilled(t),this._fulfilled()===this.howMany()&&(this._values.length=this.howMany(),1===this.howMany()&&this._unwrap?this._resolve(this._values[0]):this._resolve(this._values),!0)},i.prototype._promiseRejected=function(t){return this._addRejected(t),this._checkOutcome()},i.prototype._promiseCancelled=function(){return this._values instanceof e||null==this._values?this._cancel():(this._addRejected(u),this._checkOutcome())},i.prototype._checkOutcome=function(){if(this.howMany()>this._canPossiblyFulfill()){for(var t=new c,e=this.length();e<this._values.length;++e)this._values[e]!==u&&t.push(this._values[e]);return t.length>0?this._reject(t):this._cancel(),!0}return!1},i.prototype._fulfilled=function(){return this._totalResolved},i.prototype._rejected=function(){return this._values.length-this.length()},i.prototype._addRejected=function(t){this._values.push(t)},i.prototype._addFulfilled=function(t){this._values[this._totalResolved++]=t},i.prototype._canPossiblyFulfill=function(){return this.length()-this._rejected()},i.prototype._getRangeError=function(t){var e="Input array must contain at least "+this._howMany+" items but contains only "+t+" items";return new a(e)},i.prototype._resolveEmptyArray=function(){this._reject(this._getRangeError(0))},e.some=function(t,e){return o(t,e)},e.prototype.some=function(t){return o(this,t)},e._SomePromiseArray=i}},{"./errors":12,"./util":36}],32:[function(t,e,n){"use strict";e.exports=function(t){function e(t){void 0!==t?(t=t._target(),this._bitField=t._bitField,this._settledValueField=t._isFateSealed()?t._settledValue():void 0):(this._bitField=0,this._settledValueField=void 0)}e.prototype._settledValue=function(){return this._settledValueField};var n=e.prototype.value=function(){if(!this.isFulfilled())throw new TypeError("cannot get fulfillment value of a non-fulfilled promise\n\n    See http://goo.gl/MqrFmX\n");return this._settledValue()},r=e.prototype.error=e.prototype.reason=function(){if(!this.isRejected())throw new TypeError("cannot get rejection reason of a non-rejected promise\n\n    See http://goo.gl/MqrFmX\n");return this._settledValue()},i=e.prototype.isFulfilled=function(){return 0!=(33554432&this._bitField)},o=e.prototype.isRejected=function(){return 0!=(16777216&this._bitField)},s=e.prototype.isPending=function(){return 0==(50397184&this._bitField)},a=e.prototype.isResolved=function(){return 0!=(50331648&this._bitField)};e.prototype.isCancelled=function(){return 0!=(8454144&this._bitField)},t.prototype.__isCancelled=function(){return 65536==(65536&this._bitField)},t.prototype._isCancelled=function(){return this._target().__isCancelled()},t.prototype.isCancelled=function(){return 0!=(8454144&this._target()._bitField)},t.prototype.isPending=function(){return s.call(this._target())},t.prototype.isRejected=function(){return o.call(this._target())},t.prototype.isFulfilled=function(){return i.call(this._target())},t.prototype.isResolved=function(){return a.call(this._target())},t.prototype.value=function(){return n.call(this._target())},t.prototype.reason=function(){var t=this._target();return t._unsetRejectionIsUnhandled(),r.call(t)},t.prototype._value=function(){return this._settledValue()},t.prototype._reason=function(){return this._unsetRejectionIsUnhandled(),this._settledValue()},t.PromiseInspection=e}},{}],33:[function(t,e,n){"use strict";e.exports=function(e,n){function r(t){try{return function(t){return t.then}(t)}catch(t){return s.e=t,s}}function i(t,r,i){var a=new e(n),c=a;i&&i._pushContext(),a._captureStackTrace(),i&&i._popContext();var l=!0,u=o.tryCatch(r).call(t,(function(t){a&&(a._resolveCallback(t),a=null)}),(function(t){a&&(a._rejectCallback(t,l,!0),a=null)}));return l=!1,a&&u===s&&(a._rejectCallback(u.e,!0,!0),a=null),c}var o=t("./util"),s=o.errorObj,a=o.isObject,c={}.hasOwnProperty;return function(t,o){if(a(t)){if(t instanceof e)return t;var l=r(t);if(l===s){o&&o._pushContext();var u=e.reject(l.e);return o&&o._popContext(),u}if("function"==typeof l){if(function(t){try{return c.call(t,"_promise0")}catch(t){return!1}}(t)){u=new e(n);return t._then(u._fulfill,u._reject,void 0,u,null),u}return i(t,l,o)}}return t}}},{"./util":36}],34:[function(t,e,n){"use strict";e.exports=function(e,n,r){function i(t){this.handle=t}function o(t){return clearTimeout(this.handle),t}function s(t){throw clearTimeout(this.handle),t}var a=t("./util"),c=e.TimeoutError;i.prototype._resultCancelled=function(){clearTimeout(this.handle)};var l=function(t){return u(+this).thenReturn(t)},u=e.delay=function(t,o){var s,a;return void 0!==o?(s=e.resolve(o)._then(l,null,null,t,void 0),r.cancellation()&&o instanceof e&&s._setOnCancel(o)):(s=new e(n),a=setTimeout((function(){s._fulfill()}),+t),r.cancellation()&&s._setOnCancel(new i(a)),s._captureStackTrace()),s._setAsyncGuaranteed(),s};e.prototype.delay=function(t){return u(t,this)};var p=function(t,e,n){var r;r="string"!=typeof e?e instanceof Error?e:new c("operation timed out"):new c(e),a.markAsOriginatingFromRejection(r),t._attachExtraTrace(r),t._reject(r),null!=n&&n.cancel()};e.prototype.timeout=function(t,e){t=+t;var n,a,c=new i(setTimeout((function(){n.isPending()&&p(n,e,a)}),t));return r.cancellation()?(a=this.then(),(n=a._then(o,s,void 0,c,void 0))._setOnCancel(c)):n=this._then(o,s,void 0,c,void 0),n}}},{"./util":36}],35:[function(t,e,n){"use strict";e.exports=function(e,n,r,i,o,s){function a(t){setTimeout((function(){throw t}),0)}function c(t,n){var i=0,s=t.length,c=new e(o);return function o(){if(i>=s)return c._fulfill();var l=function(t){var e=r(t);return e!==t&&"function"==typeof t._isDisposable&&"function"==typeof t._getDisposer&&t._isDisposable()&&e._setDisposable(t._getDisposer()),e}(t[i++]);if(l instanceof e&&l._isDisposable()){try{l=r(l._getDisposer().tryDispose(n),t.promise)}catch(t){return a(t)}if(l instanceof e)return l._then(o,a,null,null,null)}o()}(),c}function l(t,e,n){this._data=t,this._promise=e,this._context=n}function u(t,e,n){this.constructor$(t,e,n)}function p(t){return l.isDisposer(t)?(this.resources[this.index]._setDisposable(t),t.promise()):t}function f(t){this.length=t,this.promise=null,this[t-1]=null}var h=t("./util"),_=t("./errors").TypeError,d=t("./util").inherits,v=h.errorObj,y=h.tryCatch,g={};l.prototype.data=function(){return this._data},l.prototype.promise=function(){return this._promise},l.prototype.resource=function(){return this.promise().isFulfilled()?this.promise().value():g},l.prototype.tryDispose=function(t){var e=this.resource(),n=this._context;void 0!==n&&n._pushContext();var r=e!==g?this.doDispose(e,t):null;return void 0!==n&&n._popContext(),this._promise._unsetDisposable(),this._data=null,r},l.isDisposer=function(t){return null!=t&&"function"==typeof t.resource&&"function"==typeof t.tryDispose},d(u,l),u.prototype.doDispose=function(t,e){return this.data().call(t,t,e)},f.prototype._resultCancelled=function(){for(var t=this.length,n=0;t>n;++n){var r=this[n];r instanceof e&&r.cancel()}},e.using=function(){var t=arguments.length;if(2>t)return n("you must pass at least 2 arguments to Promise.using");var i=arguments[t-1];if("function"!=typeof i)return n("expecting a function but got "+h.classString(i));var o,a=!0;2===t&&Array.isArray(arguments[0])?(t=(o=arguments[0]).length,a=!1):(o=arguments,t--);for(var u=new f(t),_=0;t>_;++_){var d=o[_];if(l.isDisposer(d)){var g=d;(d=d.promise())._setDisposable(g)}else{var m=r(d);m instanceof e&&(d=m._then(p,null,null,{resources:u,index:_},void 0))}u[_]=d}var b=new Array(u.length);for(_=0;_<b.length;++_)b[_]=e.resolve(u[_]).reflect();var w=e.all(b).then((function(t){for(var e=0;e<t.length;++e){var n=t[e];if(n.isRejected())return v.e=n.error(),v;if(!n.isFulfilled())return void w.cancel();t[e]=n.value()}C._pushContext(),i=y(i);var r=a?i.apply(void 0,t):i(t),o=C._popContext();return s.checkForgottenReturns(r,o,"Promise.using",C),r})),C=w.lastly((function(){var t=new e.PromiseInspection(w);return c(u,t)}));return u.promise=C,C._setOnCancel(u),C},e.prototype._setDisposable=function(t){this._bitField=131072|this._bitField,this._disposer=t},e.prototype._isDisposable=function(){return(131072&this._bitField)>0},e.prototype._getDisposer=function(){return this._disposer},e.prototype._unsetDisposable=function(){this._bitField=-131073&this._bitField,this._disposer=void 0},e.prototype.disposer=function(t){if("function"==typeof t)return new u(t,this,i());throw new _}}},{"./errors":12,"./util":36}],36:[function(t,e,n){"use strict";function r(){try{var t=u;return u=null,t.apply(this,arguments)}catch(t){return h.e=t,h}}function i(t){return null==t||!0===t||!1===t||"string"==typeof t||"number"==typeof t}function o(t,e,n){if(i(t))return t;var r={value:n,configurable:!0,enumerable:!1,writable:!0};return p.defineProperty(t,e,r),t}function s(t){try{return t+""}catch(t){return"[no string representation]"}}function a(t){return t instanceof Error||null!==t&&"object"==typeof t&&"string"==typeof t.message&&"string"==typeof t.name}function c(t){return a(t)&&p.propertyIsWritable(t,"stack")}function l(t){return{}.toString.call(t)}var u,p=t("./es5"),f="undefined"==typeof navigator,h={e:{}},_="undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:void 0!==this?this:null,d=function(){var t=[Array.prototype,Object.prototype,Function.prototype],e=function(e){for(var n=0;n<t.length;++n)if(t[n]===e)return!0;return!1};if(p.isES5){var n=Object.getOwnPropertyNames;return function(t){for(var r=[],i=Object.create(null);null!=t&&!e(t);){var o;try{o=n(t)}catch(t){return r}for(var s=0;s<o.length;++s){var a=o[s];if(!i[a]){i[a]=!0;var c=Object.getOwnPropertyDescriptor(t,a);null!=c&&null==c.get&&null==c.set&&r.push(a)}}t=p.getPrototypeOf(t)}return r}}var r={}.hasOwnProperty;return function(n){if(e(n))return[];var i=[];t:for(var o in n)if(r.call(n,o))i.push(o);else{for(var s=0;s<t.length;++s)if(r.call(t[s],o))continue t;i.push(o)}return i}}(),v=/this\s*\.\s*\S+\s*=/,y=/^[a-z$_][a-z$_0-9]*$/i,g="stack"in new Error?function(t){return c(t)?t:new Error(s(t))}:function(t){if(c(t))return t;try{throw new Error(s(t))}catch(t){return t}},m=function(t){return p.isArray(t)?t:null};if("undefined"!=typeof Symbol&&Symbol.iterator){var b="function"==typeof Array.from?function(t){return Array.from(t)}:function(t){for(var e,n=[],r=t[Symbol.iterator]();!(e=r.next()).done;)n.push(e.value);return n};m=function(t){return p.isArray(t)?t:null!=t&&"function"==typeof t[Symbol.iterator]?b(t):null}}var w,C="undefined"!=typeof process&&"[object process]"===l(process).toLowerCase(),j="undefined"!=typeof process&&void 0!==process.env,k={setReflectHandler:function(t){w=t},isClass:function(t){try{if("function"==typeof t){var e=p.names(t.prototype),n=p.isES5&&e.length>1,r=e.length>0&&!(1===e.length&&"constructor"===e[0]),i=v.test(t+"")&&p.names(t).length>0;if(n||r||i)return!0}return!1}catch(t){return!1}},isIdentifier:function(t){return y.test(t)},inheritedDataKeys:d,getDataPropertyOrDefault:function(t,e,n){if(!p.isES5)return{}.hasOwnProperty.call(t,e)?t[e]:void 0;var r=Object.getOwnPropertyDescriptor(t,e);return null!=r?null==r.get&&null==r.set?r.value:n:void 0},thrower:function(t){throw t},isArray:p.isArray,asArray:m,notEnumerableProp:o,isPrimitive:i,isObject:function(t){return"function"==typeof t||"object"==typeof t&&null!==t},isError:a,canEvaluate:f,errorObj:h,tryCatch:function(t){return u=t,r},inherits:function(t,e){function n(){for(var n in this.constructor=t,this.constructor$=e,e.prototype)r.call(e.prototype,n)&&"$"!==n.charAt(n.length-1)&&(this[n+"$"]=e.prototype[n])}var r={}.hasOwnProperty;return n.prototype=e.prototype,t.prototype=new n,t.prototype},withAppended:function(t,e){var n,r=t.length,i=new Array(r+1);for(n=0;r>n;++n)i[n]=t[n];return i[n]=e,i},maybeWrapAsError:function(t){return i(t)?new Error(s(t)):t},toFastProperties:function(t){function e(){}function n(){return typeof r.foo}e.prototype=t;var r=new e;return n(),n(),t},filledRange:function(t,e,n){for(var r=new Array(t),i=0;t>i;++i)r[i]=e+i+n;return r},toString:s,canAttachTrace:c,ensureErrorObject:g,originatesFromRejection:function(t){return null!=t&&(t instanceof Error.__BluebirdErrorTypes__.OperationalError||!0===t.isOperational)},markAsOriginatingFromRejection:function(t){try{o(t,"isOperational",!0)}catch(t){}},classString:l,copyDescriptors:function(t,e,n){for(var r=p.names(t),i=0;i<r.length;++i){var o=r[i];if(n(o))try{p.defineProperty(e,o,p.getDescriptor(t,o))}catch(t){}}},isNode:C,hasEnvVariables:j,env:function(t){return j?process.env[t]:void 0},global:_,getNativePromise:function(){if("function"==typeof Promise)try{if("[object Promise]"===l(new Promise((function(){}))))return Promise}catch(t){}},contextBind:function(t,e){if(null===t||"function"!=typeof e||e===w)return e;null!==t.domain&&(e=t.domain.bind(e));var n=t.async;if(null!==n){var r=e;e=function(){var t=new Array(2).concat([].slice.call(arguments));return t[0]=r,t[1]=this,n.runInAsyncScope.apply(n,t)}}return e}};k.isRecentNode=k.isNode&&function(){var t;return process.versions&&process.versions.node?t=process.versions.node.split(".").map(Number):process.version&&(t=process.version.split(".").map(Number)),0===t[0]&&t[1]>10||t[0]>0}(),k.nodeSupportsAsyncResource=k.isNode&&function(){var e=!1;try{e="function"==typeof t("async_hooks").AsyncResource.prototype.runInAsyncScope}catch(t){e=!1}return e}(),k.isNode&&k.toFastProperties(process);try{throw new Error}catch(t){k.lastLineError=t}e.exports=k},{"./es5":13,async_hooks:void 0}]},{},[4])(4)})),"undefined"!=typeof window&&null!==window?window.P=window.Promise:"undefined"!=typeof self&&null!==self&&(self.P=self.Promise);
