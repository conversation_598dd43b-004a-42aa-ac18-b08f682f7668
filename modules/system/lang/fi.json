{"Check For Updates": "Tarkista päivitykset", "Install Packages": "<PERSON><PERSON><PERSON>", "Manage Themes": "<PERSON>its<PERSON> tee<PERSON>ja", "Manage Plugins": "Hallitse lisäosia", "Project": "Projekti", "Owner": "Omistaja", "Plugins": "Lisäosat", "Recommended": "Suositeltu", "Disabled": "<PERSON><PERSON>", "Current Build": "Nykyinen versio", "Updates Available": "Päivityksiä saatavilla", "Up to Date": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Latest Build": "<PERSON><PERSON><PERSON> versio", "View Changelog": "Näytä muuto<PERSON>lo<PERSON>", "System Updates": "Päivitykset & Lisäosat", "Update the system modules and plugins.": "Päivitä jär<PERSON><PERSON>lm<PERSON>ä, hallitse ja asenna lis<PERSON>ia ja teemoja.", "General": "<PERSON><PERSON><PERSON>", "Mail": "Sähköposti", "Utilities": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Settings": "Asetukset", "Show All Settings": "Näytä kaikki as<PERSON>", "Unable to find the specified settings.": "Määriteltyjä asetuksia ei löytynyt.", "The settings page is missing a Model definition.": "Asetussivulta puuttuu mallin määritelmä.", ":name settings updated": ":name as<PERSON><PERSON><PERSON>", "Return to System Settings": "<PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Find a Setting...": "<PERSON><PERSON><PERSON> asetus...", "Disable mail branding CSS": "Poista sähköpostin brändäys CSS käytöstä", "Manage Sites": "<PERSON><PERSON><PERSON> si<PERSON>", "Manage the websites available for this application.": "Hallinnoi tälle sovellukselle saatavilla olevia verkkosivustoja.", "Marketplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "There is no documentation provided.": "Dokumentaatiota ei lö<PERSON>y.", "Documentation": "Dokumentaatio", "Upgrade Guide": "Päivitysopas", "License": "<PERSON><PERSON><PERSON>", "Attach to Project": "Kiinnitä projektiin", "Manage Updates": "Hallinnoi p<PERSON>ivityksiä", "Software Update": "Ohjelmistopäivitys", "Return to System Updates": "<PERSON><PERSON>a j<PERSON>stelmäpäivityksiin", "Try Again": "<PERSON><PERSON><PERSON>", "Unpacking application files": "<PERSON><PERSON><PERSON>", "Update Failed": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "Unpacking plugin: :name": "Puretaan lisäosaa: :name", "The primary site is used by default and cannot be deleted.": "Ensisijaista sivustoa k<PERSON>ytetään o<PERSON>, eikä sitä voi poistaa.", "Disabled sites are not shown on the frontend.": "Käytöstä poistettuja sivustoja ei näytetä käyttöliittymässä.", "Enabled in the Admin Panel": "Käytössä <PERSON>", "Configuration": "Kokoonpano", "Use this if you want the site to be enabled in the admin panel.": "<PERSON><PERSON><PERSON><PERSON> tätä, jos halu<PERSON>, että sivusto otetaan käyttöön hallintapaneelissa.", "Install": "<PERSON><PERSON><PERSON>", "Sync Project": "Synkronoi projekti", "Name": "<PERSON><PERSON>", "Unique Code": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> koodi", "Theme": "<PERSON><PERSON>", "Sites": "Sivustot", "Create Site": "<PERSON><PERSON> sivusto", "Base URL": "Oletus <PERSON>-osoite", "Status": "Tilakoodi", "Current default value: :value": "Nykyinen oletusarvo: :value", "Locale": "<PERSON><PERSON><PERSON><PERSON>", "Timezone": "Ai<PERSON><PERSON><PERSON><PERSON><PERSON>", "Custom application URL": "Mukautetun sovelluksen URL-osoite", "Override the application URL when this site is active.": "<PERSON><PERSON>sen URL-osoite, kun tämä sivusto on aktiivinen.", "Use a CMS route prefix": "Käytä CMS-reitin etuliitettä", "A prefix can identify this site when using a shared hostname.": "Etuliite voi tunnistaa tämän sivuston, kun käytetään jaettua isäntänimeä.", "Define matching hostnames": "Määritä vastaavat isäntänimet", "Specify domain names and patterns that must be used to serve this site.": "Määritä verkkotunnusten nimet ja mallit, joita on käytettävä tämän sivuston palvelemiseen.", "Display a style for this site": "Näytä tämän sivuston väri", "To help identify this site, display a color in the admin panel.": "Voit helpottaa tämän sivuston tunnistamista näyttämällä värin hallintapaneelis<PERSON>.", "Save": "<PERSON><PERSON><PERSON>", "Save and Close": "<PERSON><PERSON>na ja sulje", "Use Default": "Käytä oletusta", "Use Custom": "Käytä mukautettua", "Specify a custom locale code.": "Määritä mukautettu alue<PERSON>odi.", "Failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "or": "tai", "Code": "<PERSON><PERSON><PERSON>", "October CMS Marketplace": "OctoberCMS Markkinapaikka", "Visit the :link to add some.": "Projektissa ei ole lisäosia tai teemoja. Käy :linkissä lisätäks<PERSON> joit<PERSON>.", "Buy Now": "Osta nyt", "Updating package manager": "Päivitetään p<PERSON>", "Updating application files": "<PERSON><PERSON><PERSON> oh<PERSON> versiota", "Setting build number": "Asetetaan versionumeroa", "Finishing update process": "Viimeistellään p<PERSON>ivitysprosessia", "Installing plugin: :name": "Ladataan lisäosaa: :name", "Finishing installation process": "Viimeistellää<PERSON>", "Removing theme: :name": "Poistetaan teemaa: :name", "Please specify a Theme name to install.": "<PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON><PERSON><PERSON> teeman nimi.", "Installing theme: :name": "Ladataan teemaa: :name", "Extracting theme: :name": "Puretaan teemaa: :name", "Seeding theme: :name": "Lähetetään teema: :name", "Removing plugin: :name": "Poistetaan lis<PERSON>osaa: :name", "Please specify a Plugin name to install.": "Määritä asennettavan lis<PERSON>osan nimi.", "Update process complete": "<PERSON><PERSON><PERSON><PERSON> valmis", "Package installed successfully": "<PERSON><PERSON><PERSON><PERSON> asennettu onnist<PERSON>esti", "Check Dependencies": "Tarkista r<PERSON>", "Install Dependencies": "<PERSON><PERSON><PERSON>", "There are missing dependencies needed for the system to run correctly.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> o<PERSON>an toiminnan kannalta tarvittavia riippuvuuksia puuttuu.", "License Key": "Lisenssiavain", "How to find your License Key": "Kuinka löytää lisenssiavain", "The password attribute is required.": "Salasana-<PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen.", "The login attribute is required.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on pakollinen.", "Cannot login user since they are not activated.": "Käyttäjää ei voi kirjautua sisään, koska käyttäjää ei ole aktivoitu.", "Cannot login user since they are banned.": "Käyttäjää ei voi kirjautua sis<PERSON>än, koska käyttäjä on estetty.", "Cannot login user since they are suspended.": "Käyttäjää ei voi kirjautua sis<PERSON>än, koska käyttäj<PERSON> on keskeytetty.", "A user was not found with the given credentials.": "Käyttäjää ei löydy annetuil<PERSON> tunnuksilla.", "A user was found but the password did not match.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mutta salasana ei vastannut.", "User is not logged in": "Käyttäjä ei ole kirjautunut sisään", "Register Software": "Rekister<PERSON><PERSON>", "View the Dashboard": "Näytä koon<PERSON>äkymä", "Set the Default Dashboard": "<PERSON><PERSON> oletus k<PERSON>", "Log File": "<PERSON><PERSON><PERSON><PERSON>", "Sendmail": "Sendmail", "SMTP": "SMTP", "Mailgun": "Mailgun", "SES": "SES", "Postmark": "Postmark", "No encryption": "<PERSON><PERSON> sa<PERSON>ta", "TLS": "TLS", "Mail Configuration": "Sähköpostin asetukset", "Manage email configuration.": "Hallitse sähköpostin asetuksia.", "Sender Name": "Lähettäjän nimi", "Sender Email": "Lähettäjän sähköposti", "Mail Method": "Sähköpostimetodi", "SMTP Address": "SMTP-osoite", "SMTP Port": "SMTP-portti", "SMTP Encryption Protocol": "SMTP salauksen protokolla", "SMTP Authorization Required": "SMTP-palvelimen valtuutus vaaditaan", "Use this checkbox if your SMTP server requires authorization.": "Käytä tätä vali<PERSON>, jos <PERSON>-palvelin vaatii val<PERSON><PERSON>.", "Username": "Käyttäjänimi", "Password": "<PERSON><PERSON><PERSON>", "Sendmail Path": "<PERSON><PERSON><PERSON> polku", "Please specify the path of the sendmail program.": "Määritä sendmail-<PERSON><PERSON><PERSON> polku.", "Mailgun Domain": "Mailgun-verkkotunnus", "Please specify the Mailgun domain name.": "Määritä Mailgun verkkotunnus.", "Mailgun Secret": "Mailgun Secret-koodi", "Enter your Mailgun API key.": "Syötä Mailgun API -avain.", "SES Key": "SES-avain", "Enter your SES API key": "Syötä SES API-avain", "SES Secret": "SES Secret-koodi", "Enter your SES API secret key": "Syötä SES APIn Secret-avain", "SES Region": "SES-alue", "Enter your SES region (e.g. us-east-1)": "<PERSON><PERSON><PERSON><PERSON> (esim. us-east-1)", "Postmark Token": "Postmark Token", "Enter your Postmark API secret key": "Syötä Postmark API-avain", "Define administrator roles": "Määritä ylläpitäjän roolit", "Restrict access to this site to only administrator with the following roles.": "<PERSON><PERSON><PERSON> pä<PERSON>ä tälle sivustolle vain ylläpitäjälle, jolla on se<PERSON><PERSON>t roolit.", "Group": "<PERSON><PERSON><PERSON><PERSON>", "Create Group": "<PERSON><PERSON>", "Site Group": "<PERSON><PERSON><PERSON> ryhm<PERSON>", "Site Groups": "<PERSON><PERSON>ston ryhmät", "Manage Site Groups": "<PERSON><PERSON><PERSON> sivuston ryhmi<PERSON>", "Group Name": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "All Sites": "<PERSON><PERSON><PERSON>", "Manage Backend Preferences": "Hallitse ylläpitopuolen asetuksia", "Manage Code Editor Preferences": "<PERSON>itse koodieditorin as<PERSON>", "Customize Backend Styles": "Muokkaa ylläpitoa", "Mail Templates": "Sähköpostipohjat", "Modify the mail templates that are sent to users and administrators, manage email layouts.": "Muokkaa sähköpostimalleja, jotka lähetetään käyttäjille ja järjestelmänvalvojille, hallitse sähköpostien ulkoasua.", "Mail Branding": "Sähköpostin brändäys", "Modify the colors and appearance of mail templates.": "Muokkaa sähköpostiviestin värejä ja teemaa.", "Event Log": "Ta<PERSON>ht<PERSON>lo<PERSON>", "View system log messages with their recorded time and details.": "Näytä järjestelmän lokiviesteihin tallennettu aika ja tiedot.", "Request Log": "P<PERSON>yntöloki", "View bad or redirected requests, such as Page not found (404).": "Tarkastele huonoja tai uudelleenohjattuja p<PERSON>yntöjä, kuten Sivu<PERSON> ei lö<PERSON>y (404).", "Log Settings": "Lokiasetukset", "Specify which areas should use logging.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, mitä <PERSON>an lokiin.", "Test message sent.": "Testiviesti lähetetty.", "New Layout": "<PERSON><PERSON><PERSON>", "New Partial": "<PERSON><PERSON><PERSON>", "New Template": "<PERSON><PERSON><PERSON> teema", "Template": "<PERSON><PERSON>", "Templates": "<PERSON><PERSON><PERSON>", "Layouts": "Ulk<PERSON><PERSON><PERSON>", "Partials": "Sisältöpalat", "Partial": "Sisältöpala", "Mail Partials": "Sähköpostin sisältöpalat", "Layout": "<PERSON><PERSON><PERSON><PERSON>", "Mail Layouts": "Sähköpostin ulko<PERSON>ut", "Unique code used to refer to this template": "Uniikkikoodi teemalle", "-- No layout --": "-- <PERSON><PERSON> --", "HTML": "HTML", "CSS": "CSS", "Plaintext": "Pelkkä teksti", "Disable automatic inline CSS": "Poista automaattinen inline CSS", "Options": "Optiot", "Subject": "<PERSON><PERSON><PERSON><PERSON>", "Email message subject": "Sähköpostin aihe", "Description": "<PERSON><PERSON><PERSON>", "Drivers Not Installed": "A<PERSON>ita ei asennettu", "This mail method requires the plugin \":plugin\" be installed before you can send mail.": "Tämä sähköpostimenetelmä vaatii lisäosan \":plugin\" asennettavaksi ennen kuin voit lähettää sähköpostia.", "ID": "ID", "Event ID": "Tapahtuman ID", "Level": "<PERSON><PERSON>", "Date & Time": "Päivä & Aika", "Message": "<PERSON><PERSON><PERSON>", "Logging": "<PERSON><PERSON>", "Log Bad Requests": "<PERSON><PERSON><PERSON><PERSON>", "Browser requests that may require attention, such as 404 errors.": "<PERSON><PERSON><PERSON>, jot<PERSON> v<PERSON><PERSON>, kuten 404 virheet.", "Log Theme Changes": "<PERSON><PERSON><PERSON><PERSON> mu<PERSON>", "When a change is made to the theme using the backend.": "<PERSON><PERSON> muutos on tehty teemaan käyttäen hallintapaneelia.", "Log System Events": "<PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON><PERSON> tap<PERSON>", "Store system events in the database in addition to the file-based log.": "<PERSON><PERSON>na j<PERSON>stelmätapahtumat tietok<PERSON>aan tiedostopohjaisen lokin lisäksi.", "Background": "Tausta", "Body background": "Body-tausta", "Content background": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ta<PERSON>a", "Inner content background": "Keskeisen si<PERSON>ällön tausta", "Buttons": "Painikkeet", "Button text color": "Painikkeiden tekstin väri", "Primary button background": "Ensisijaisen <PERSON> tausta", "Positive button background": "Positiivisen painik<PERSON>en tausta", "Negative button background": "Negatiivisen painik<PERSON>en tausta", "Typography": "Typografia", "Header color": "Pääosion väri", "Headings color": "Otsikoiden väri", "Text color": "<PERSON><PERSON><PERSON>", "Link color": "Linkkiväri", "Footer color": "Alatunnisteen väri", "Borders": "<PERSON><PERSON><PERSON>", "Body border color": "Pääosion reunuksen väri", "Subcopy border color": "<PERSON><PERSON><PERSON><PERSON> väri", "Table border color": "Taulukon reunuksen väri", "Components": "Komponentit", "Panel background": "<PERSON><PERSON><PERSON> tausta", "Promotion background": "Promootion tausta", "Promotion border color": "Promootion reunan väri", "Customize Mail Appearance": "Muokkaa sähköpostiviestin ulkoasua", "Version": "Versio", "Enabled": "Käytössä", "Latest": "Uusimmat", "Log ID": "Loki ID", "Counter": "<PERSON><PERSON><PERSON>", "Referers": "Viitteet", "URL": "URL", "This log displays a list of potential errors that occur in the application, such as exceptions and debugging information.": "Tämä loki näyttää listan mahdollisista virheistä, jotka tapahtuvat sovelluksessa, kuten poikkeukset ja virheen korja<PERSON>.", "Event": "Tapahtuma", "This log displays a list of browser requests that may require attention. For example, if a visitor opens a CMS page that cannot be found, a record is created with the status code 404.": "Tämä loki näyttää listan selainten <PERSON>, jotka sa<PERSON>avat vaatia huomiotasi. Esim. jos vierailijat avaavat CMS-sivun, jota ei l<PERSON>, kohde lisätään tilakoodilla 404.", "There were no detected referrers to this URL.": "Tähän URL-osoitteeseen ei löytynyt viittauksia.", "Request": "Pyyntö", "Event log emptied": "Tapahtumaloki on tyhjä", "Empty Event Log": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Emptying Event Log...": "Tyhjennet<PERSON><PERSON><PERSON> tap<PERSON>...", "Return to Event Log": "<PERSON><PERSON><PERSON>", "Installed": "Asennett<PERSON>", "Primary Site": "<PERSON><PERSON><PERSON><PERSON> sivusto", "Refresh Data": "<PERSON><PERSON><PERSON><PERSON><PERSON> tiedot", "Remove Data": "<PERSON><PERSON><PERSON><PERSON> tiedot", "Are you sure you want to reset the selected plugins? This will reset each plugin's data, restoring it to the initial install state.": "<PERSON><PERSON><PERSON>, että haluat palauttaa valittujen liitännäisten tiedot? Tämä palauttaa jokaisen liitännäisen datan alkuperäiseen asennustilaan.", "Reset Plugin Data": "<PERSON><PERSON><PERSON>", "Enable Plugins": "<PERSON><PERSON> liitännä<PERSON>t k<PERSON>yttöön", "Are you sure you want to :action these plugins?": "<PERSON><PERSON><PERSON>, että haluat :action nämä liitännäiset?", "enable": "käytä", "disable": "poista k<PERSON>", "Disable Plugins": "Poista liitännäiset käytöstä", "Select Action...": "Valitse toiminto...", "Data has been removed.": "<PERSON><PERSON><PERSON> on poistettu.", "Plugin has been removed from the file system.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on poistettu tiedostojärjestelmästä.", "search plugins to install...": "etsi asennettavia liitännäisiä...", "Plugin has been disabled by configuration.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on poistettu käytöstä as<PERSON>ten takia.", "Plugin has missing dependencies or disabled by system.": "<PERSON>itä<PERSON><PERSON><PERSON><PERSON><PERSON> on puuttuvia riippuvuuksia tai se on poistettu käytöstä järjestelmän toimesta.", "search themes to install...": "etsi asennettavia teemoja...", "Install Theme": "<PERSON><PERSON><PERSON> teema", "Theme Name": "<PERSON><PERSON> nimi", "Install Plugin": "<PERSON><PERSON><PERSON>", "Plugin Name": "Lisäosan nimi", "Name the plugin by its unique code. For example, RainLab.Blog": "<PERSON> lis<PERSON> un<PERSON>i. Esimerkiksi: RainLab.Blog", "Name the theme by its unique code. For example, RainLab.Vanilla": "Nimeä teema uniikilla tunnuksella. Esimerkiksi RainLab.Vanilla", "Select Installation Method": "Valitse asennustapa", "Make a Copy (Recommended)": "<PERSON><PERSON> kop<PERSON> (suositus)", "Take a copy of this theme to customize it and manually manage future updates.": "Ota kopio tästä teemasta muokkaaksesi sitä ja hoida tulevat päivityks<PERSON> käsin.", "Install with Composer": "<PERSON><PERSON><PERSON>", "Extend the theme using a child theme to preserve future updates from the theme author.": "Laaj<PERSON>na teemaa lapsiteeman avulla säilyttääksesi tulevat päivitykset teeman tekijältä.", "Seed Theme Data": "<PERSON><PERSON> teeman data", "Import the blueprints files, language files and database contents for this theme, if applicable. You can skip this and do it later via the Frontend Theme settings.": "<PERSON><PERSON> tämän teeman Blueprint-mallit, kielitiedostot ja tietokannan sisäll<PERSON>t tarvittaessa. Voit ohittaa tämän ja tehdä sen my<PERSON><PERSON>min Frontend-teemaasetusten kautta.", "Change Status": "<PERSON><PERSON> tila", "Site Definitions": "Sivuston määritykset"}