{"Check For Updates": "Проверить наличие обновлений", "Install Packages": "Установить пакеты", "Manage Themes": "Управление темами", "Manage Plugins": "Управление плагинами", "Project": "Проект", "Owner": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Plugins": "Плагины", "Recommended": "Рекомендуется", "Disabled": "Отключено", "Current Build": "Текущая сборка", "Updates Available": "Доступны обновления", "Up to Date": "Последняя версия", "Latest Build": "Последняя сборка", "View Changelog": "Посмотреть список изменений", "System Updates": "Обновления системы", "Update the system modules and plugins.": "Обновление системных модулей и плагинов.", "General": "Общее", "Mail": "Почта", "Utilities": "Утилиты", "Settings": "Настройки", "Show All Settings": "Показать все настройки", "Unable to find the specified settings.": "Не удается найти указанные настройки.", "The settings page is missing a Model definition.": "На странице настроек отсутствует определение модели.", ":name settings updated": "Настройки :name успешно обновлены.", "Return to System Settings": "Вернуться к системным настройкам", "Find a Setting...": "Найти настройки...", "Disable mail branding CSS": "Отключить CSS для брендинга почты", "Manage Sites": "Управление сайтами", "Manage the websites available for this application.": "Управление веб-сайтами, доступными для этого приложения.", "Marketplace": "Маркетплейс", "There is no documentation provided.": "Документация не предоставлена.", "Documentation": "Документация", "Upgrade Guide": "Инструкция по обновлению", "License": "Лицензия", "Attach to Project": "Добавить в проект", "Manage Updates": "Менеджер обновлений", "Software Update": "Обновление системы", "Return to System Updates": "Вернуться к системе обновлений", "Try Again": "Попробовать еще раз", "Unpacking application files": "Распаковка файлов приложения", "Update Failed": "Не удалось выполнить обновление", "Unpacking plugin: :name": "Распаковка плагина: :name", "The primary site is used by default and cannot be deleted.": "Основной сайт используется по умолчанию и не может быть удален.", "Disabled sites are not shown on the frontend.": "Отключенные сайты не отображаются на фронтенде.", "Enabled in the Admin Panel": "Включено в панели администратора", "Configuration": "Конфигурация", "Use this if you want the site to be enabled in the admin panel.": "Используйте это, если хотите, чтобы сайт был активен в панели администратора.", "Install": "Установить", "Sync Project": "Синхронизировать проект", "Name": "Название", "Unique Code": "Уникальный код", "Theme": "Тема", "Sites": "Сайты", "Create Site": "Создать сайт", "Base URL": "Базовый URL", "Status": "Статус", "Current default value: :value": "Текущее значение по умолчанию: :value", "Locale": "Язык", "Timezone": "Часовой пояс", "Custom application URL": "Пользовательский URL приложения", "Override the application URL when this site is active.": "Переопределить URL приложения, когда этот сайт активен.", "Use a CMS route prefix": "Использовать префикс роута CMS", "A prefix can identify this site when using a shared hostname.": "Префикс может идентифицировать этот сайт при использовании общего имени хоста.", "Define matching hostnames": "Введите совпадающие имена хостов", "Specify domain names and patterns that must be used to serve this site.": "Укажите доменные имена и паттерны, которые должны использоваться для этого сайта.", "Display a style for this site": "Включить цвет для этого сайта", "To help identify this site, display a color in the admin panel.": "Чтобы помочь идентифицировать этот сайт, отобразите цвет в панели администратора.", "Save": "Сохранить", "Save and Close": "Сохранить и закрыть", "Use Default": "По умолчанию", "Use Custom": "Использовать ", "Specify a custom locale code.": "Укажите пользовательский код локали.", "Failed": "Ошибка", "or": "или", "Code": "<PERSON>од", "October CMS Marketplace": "Маркетплейс October CMS", "Visit the :link to add some.": "У проекта нет плагинов или тем. Перейдите в :link, чтобы добавить их.", "Buy Now": "Купить", "Updating package manager": "Обновление пакетного менеджера", "Updating application files": "Обновление файлов приложения", "Setting build number": "Установка номера сборки", "Finishing update process": "Завершение процесса обновления", "Installing plugin: :name": "Загрузка плагина: :name", "Finishing installation process": "Завершение процесса установки", "Removing theme: :name": "Удаление темы: :name", "Please specify a Theme name to install.": "Пожалуйста, укажите название темы для установки.", "Installing theme: :name": "Установка темы: :name", "Extracting theme: :name": "Извлечение темы: :name", "Seeding theme: :name": "Сидинг темы: :name", "Removing plugin: :name": "Удаление плагина: :name", "Please specify a Plugin name to install.": "Пожалуйста, укажите название плагина для установки.", "Update process complete": "Процесс обновления завершен", "Package installed successfully": "Плагин успешно установлен", "Check Dependencies": "Проверить зависимости", "Install Dependencies": "Установить зависимости", "There are missing dependencies needed for the system to run correctly.": "Отсутствуют зависимости, которые необходимы для корректной работы системы.", "License Key": "Лицензионный ключ", "How to find your License Key": "Как найти свой лицензионный ключ?", "The password attribute is required.": "Пароль обязателен.", "The login attribute is required.": "Логин обязателен.", "Cannot login user since they are not activated.": "Невозможно авторизоваться так как пользователь не активирован.", "Cannot login user since they are banned.": "Невозможно авторизоваться так как пользователь заблокирован.", "Cannot login user since they are suspended.": "Невозможно авторизоваться так как действия пользователя приостановлены.", "A user was not found with the given credentials.": "Пользователь с указанными данными не найден.", "A user was found but the password did not match.": "Пользователь найден, но пароль не совпадает.", "User is not logged in": "Пользователь не авторизован", "Register Software": "Регистрация ПО", "View the Dashboard": "Просмотр дашборда", "Set the Default Dashboard": "Установить дашборд по умолчанию", "Log File": "<PERSON>айл жур<PERSON>ла", "Sendmail": "Sendmail", "SMTP": "SMTP", "Mailgun": "Mailgun", "SES": "SES", "Postmark": "Postmark", "No encryption": "Без шифрования", "TLS": "TLS", "Mail Configuration": "Настройки почты", "Manage email configuration.": "Управление настройками почты.", "Sender Name": "Имя отправителя", "Sender Email": "Адрес отправителя", "Mail Method": "Способ отправки почты", "SMTP Address": "SMTP адрес", "SMTP Port": "SMTP порт", "SMTP Encryption Protocol": "Протокол шифрования SMTP", "SMTP Authorization Required": "Использовать SMTP авторизацию", "Use this checkbox if your SMTP server requires authorization.": "Активируйте эту опцию, если ваш SMTP-сервер требует авторизацию.", "Username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Password": "Пароль", "Sendmail Path": "Путь к Sendmail", "Please specify the path of the sendmail program.": "Пожалуйста, укажите путь к sendmail.", "Mailgun Domain": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Please specify the Mailgun domain name.": "Пожалуйста, укажите домен Mailgun.", "Mailgun Secret": "Серекетный ключ Mailgun", "Enter your Mailgun API key.": "Введите ваш Mailgun API-ключ.", "SES Key": "SES API-ключ", "Enter your SES API key": "Введите ваш SES API-ключ", "SES Secret": "SES секретный API-ключ", "Enter your SES API secret key": "Введите ваш секретный SES API-ключ", "SES Region": "SES регион", "Enter your SES region (e.g. us-east-1)": "Введите свой SES регион (например: us-east-1)", "Postmark Token": "Postmark токен", "Enter your Postmark API secret key": "Введите секретный ключ Postmark API", "Define administrator roles": "Задайте роли администра<PERSON><PERSON><PERSON>ов", "Restrict access to this site to only administrator with the following roles.": "Ограничить доступ к этому сайту только администраторам со следующими ролями.", "Group": "Группа", "Create Group": "Создать группу", "Site Group": "Группа сайта", "Site Groups": "Группы сайтов", "Manage Site Groups": "Управление группами сайтов", "Group Name": "Название группы", "All Sites": "Все сайты", "Manage Backend Preferences": "Управление настройками панели управления", "Manage Code Editor Preferences": "Управление настройками редактора кода", "Customize Backend Styles": "Персонализация панели управления", "Mail Templates": "Почтовые шаблоны", "Modify the mail templates that are sent to users and administrators, manage email layouts.": "Изменение почтовых шаблонов, отправляемых пользователям и администраторам.", "Mail Branding": "Почтовый брендинг", "Modify the colors and appearance of mail templates.": "Измените цвета и внешний вид почтовых шаблонов.", "Event Log": "<PERSON><PERSON><PERSON><PERSON><PERSON> событий", "View system log messages with their recorded time and details.": "Просмотр системного журнала событий.", "Request Log": "<PERSON><PERSON><PERSON><PERSON><PERSON> запрос<PERSON>", "View bad or redirected requests, such as Page not found (404).": "Просмотр неудачных или перенаправленных запросов.", "Log Settings": "Настрой<PERSON><PERSON> жур<PERSON>лов", "Specify which areas should use logging.": "Укажите для каких частей CMS следует вести журнал.", "Test message sent.": "Тестовое сообщение успешно отправлено.", "New Layout": "Новый шаблон", "New Partial": "Новый фрагмент", "New Template": "Новый шаблон", "Template": "Шабл<PERSON>н", "Templates": "Шаблоны", "Layouts": "Шаблоны", "Partials": "Фрагменты", "Partial": "Фрагмент", "Mail Partials": "Почтовые фрагменты", "Layout": "Шабл<PERSON>н", "Mail Layouts": "Почтовые шаблоны", "Unique code used to refer to this template": "Уникальный код, используемый для обозначения этого шаблона", "-- No layout --": "-- Без шаблона --", "HTML": "HTML", "CSS": "CSS", "Plaintext": "Простой текст", "Disable automatic inline CSS": "Отключить автоматическую вставку inline-стилей (inline CSS)", "Options": "Настройки", "Subject": "Тема", "Email message subject": "Тема сообщения", "Description": "Описание", "Drivers Not Installed": "Плагин October.Drivers не установлен", "This mail method requires the plugin \":plugin\" be installed before you can send mail.": "Этот почтовый метод требует плагин \":plugin\", чтобы отправлять письма.", "ID": "ID", "Event ID": "ID события", "Level": "Уровень", "Date & Time": "Дата и время", "Message": "Сообщение", "Logging": "Ж<PERSON>рналирование", "Log Bad Requests": "Сохранять неудачные запросы", "Browser requests that may require attention, such as 404 errors.": "Запросы браузера, которые могут потребовать внимания. Например, ошибки 404.", "Log Theme Changes": "Сохранять изменения темы в журнал", "When a change is made to the theme using the backend.": "При внесении изменений в тему из панели администратора.", "Log System Events": "Сохранять системные события", "Store system events in the database in addition to the file-based log.": "Сохранять системные события в базе данных в дополнение к файловому журналу.", "Background": "Фон", "Body background": "Фон документа", "Content background": "Фон контента", "Inner content background": "Внутренний фон контента", "Buttons": "Кнопки", "Button text color": "Цвет текста кнопки", "Primary button background": "Цвет основной кнопки", "Positive button background": "Цвет положительной кнопки", "Negative button background": "Цвет отрицательной кнопки", "Typography": "Типографика", "Header color": "Цвет заголовка", "Headings color": "Цвет заголовков", "Text color": "Цвет текста", "Link color": "Цвет ссылки", "Footer color": "Цвет футера", "Borders": "<PERSON>р<PERSON><PERSON><PERSON><PERSON><PERSON>", "Body border color": "Цвет границ документа", "Subcopy border color": "Цвет границы субкопии", "Table border color": "Цвет границ таблицы", "Components": "Компоненты", "Panel background": "Фон панели", "Promotion background": "Фон промо блока", "Promotion border color": "Цвет рамки промо блока", "Customize Mail Appearance": "Настройка внешнего вида почты", "Version": "Версия", "Enabled": "Включено", "Latest": "Последняя версия", "Log ID": "ID записи", "Counter": "Счетчик", "Referers": "Источник запроса", "URL": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "This log displays a list of potential errors that occur in the application, such as exceptions and debugging information.": "В этом журнале отображается список возможных ошибок, которые возникают в работе приложения, таких как исключения и отладочная информация.", "Event": "События", "This log displays a list of browser requests that may require attention. For example, if a visitor opens a CMS page that cannot be found, a record is created with the status code 404.": "В этом журнале отображается список запросов браузера, которые могут потребовать внимания. Например, если посетитель открывает несуществующую страницу, то в журнале создается запись с кодом статуса 404.", "There were no detected referrers to this URL.": "Не обнаружено ссылок на этот URL.", "Request": "Запрос", "Event log emptied": "Жу<PERSON><PERSON>л событий очищен", "Empty Event Log": "Очистить журнал событий", "Emptying Event Log...": "Очищение журнала событий...", "Return to Event Log": "Вернуться в журнал событий", "Installed": "Установлено", "Primary Site": "Основной сайт", "Refresh Data": "Обновить данные", "Remove Data": "Удалить данные", "Are you sure you want to reset the selected plugins? This will reset each plugin's data, restoring it to the initial install state.": "Вы уверены, что хотите переустановить выбранные плагины? Это сбросит данные каждого плагина, вернув его к исходному состоянию.", "Reset Plugin Data": "Сбросить данные плагина", "Enable Plugins": "Включить плагины", "Are you sure you want to :action these plugins?": "Вы действительно хотите :action?", "enable": "включить", "disable": "выключить", "Disable Plugins": "Выключить плагины", "Select Action...": "Выберите действие...", "Data has been removed.": "Данные были удалены.", "Plugin has been removed from the file system.": "Плагин был удален из файловой системы.", "search plugins to install...": "Поиск плагинов...", "Plugin has been disabled by configuration.": "Плагин был отклчен конфигурацией.", "Plugin has missing dependencies or disabled by system.": "Плагин имеет отсуствующие зависимости или отключен системой.", "search themes to install...": "поиск шаблонов для установки...", "Install Theme": "Установить тему", "Theme Name": "Название темы", "Install Plugin": "Установить плагин", "Plugin Name": "Имя плагина", "Name the plugin by its unique code. For example, RainLab.Blog": "Введите название плагина со своим уникальным кодом. Например, RainLab.Blog", "Name the theme by its unique code. For example, RainLab.Vanilla": "Название темы по ее уникальному коду. Например, RainLab.Vanilla", "Select Installation Method": "Выберите метод установки", "Make a Copy (Recommended)": "Сделать копию (рекомендуется)", "Take a copy of this theme to customize it and manually manage future updates.": "Сделайте копию этой темы, чтобы настроить ее и вручную управлять будущими обновлениями.", "Install with Composer": "Установить с помощью Composer", "Extend the theme using a child theme to preserve future updates from the theme author.": "Расширьте тему, используя дочернюю тему, чтобы сохранить будущие обновления от автора темы.", "Seed Theme Data": "Сидинг данных темы", "Import the blueprints files, language files and database contents for this theme, if applicable. You can skip this and do it later via the Frontend Theme settings.": "Импортировать чертежи, языковые файлы и содержимое базы данных для этой темы. Это можно пропустить и сделать позже в настройках темы.", "Change Status": "Изменить статус", "Site Definitions": "Сайты"}