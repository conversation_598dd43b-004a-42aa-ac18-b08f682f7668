{"Check For Updates": "Verificar actualizações", "Install Packages": "Install Packages", "Manage Themes": "Manage Themes", "Manage Plugins": "<PERSON><PERSON><PERSON>", "Project": "Projecto", "Owner": "<PERSON><PERSON><PERSON><PERSON>", "Plugins": "Extensões", "Recommended": "Recommended", "Disabled": "Desactivados", "Current Build": "Compilação atual", "Updates Available": "Updates Available", "Up to Date": "Up to Date", "Latest Build": "Latest Build", "View Changelog": "View Changelog", "System Updates": "Actualizações", "Update the system modules and plugins.": "Actualize o sistema, gira e instale extensões e temas.", "General": "G<PERSON>", "Mail": "Mail", "Utilities": "Utilities", "Settings": "Configurações", "Show All Settings": "Show All Settings", "Unable to find the specified settings.": "Impossível encontrar as configurações solicitadas.", "The settings page is missing a Model definition.": "Falta uma definição de modelo na página de configurações.", ":name settings updated": "Configurações para :name foram atualizados com sucesso.", "Return to System Settings": "Regressar para as configurações do sistema", "Find a Setting...": "Find a Setting...", "Disable mail branding CSS": "Disable mail branding CSS", "Manage Sites": "Manage Sites", "Manage the websites available for this application.": "Manage the websites available for this application.", "Marketplace": "Marketplace", "There is no documentation provided.": "Não foi fornecida nenhuma documentação.", "Documentation": "Documentação", "Upgrade Guide": "Guia de actualização", "License": "Licença", "Attach to Project": "Anexar ao projecto", "Manage Updates": "<PERSON><PERSON><PERSON>", "Software Update": "Actualização de software", "Return to System Updates": "Voltar às actualizações", "Try Again": "Tentar novamente", "Unpacking application files": "Descomprimindo ficheiros do aplicação", "Update Failed": "Falha na actualização", "Unpacking plugin: :name": "Descomprimindo a extensão: :name", "The primary site is used by default and cannot be deleted.": "The primary site is used by default and cannot be deleted.", "Disabled sites are not shown on the frontend.": "Disabled sites are not shown on the frontend.", "Enabled in the Admin Panel": "Enabled in the Admin Panel", "Configuration": "Configuration", "Use this if you want the site to be enabled in the admin panel.": "Use this if you want the site to be enabled in the admin panel.", "Install": "Install", "Sync Project": "Sync Project", "Name": "Nome", "Unique Code": "Unique Code", "Theme": "Theme", "Sites": "Sites", "Create Site": "Create Site", "Base URL": "Base URL", "Status": "Estado", "Current default value: :value": "Current default value: :value", "Locale": "Locale", "Timezone": "Timezone", "Custom application URL": "Custom application URL", "Override the application URL when this site is active.": "Override the application URL when this site is active.", "Use a CMS route prefix": "Use a CMS route prefix", "A prefix can identify this site when using a shared hostname.": "A prefix can identify this site when using a shared hostname.", "Define matching hostnames": "Define matching hostnames", "Specify domain names and patterns that must be used to serve this site.": "Specify domain names and patterns that must be used to serve this site.", "Display a style for this site": "Display a style for this site", "To help identify this site, display a color in the admin panel.": "To help identify this site, display a color in the admin panel.", "Save": "Save", "Save and Close": "Save and Close", "Use Default": "Use Default", "Use Custom": "Use Custom", "Specify a custom locale code.": "Specify a custom locale code.", "Failed": "Failed", "or": "or", "Code": "Código", "October CMS Marketplace": "October CMS Marketplace", "Visit the :link to add some.": "Project has no plugins or themes. Visit the :link to add some.", "Buy Now": "Buy Now", "Updating package manager": "Updating package manager", "Updating application files": "Descarregando ficheiros da aplicação", "Setting build number": "Setting build number", "Finishing update process": "Finalizando processo de actualização", "Installing plugin: :name": "Baixando a extensão: :name", "Finishing installation process": "Finalizando o processo de instalação", "Removing theme: :name": "Removing theme: :name", "Please specify a Theme name to install.": "Por favor, especifique um nome de tema para instalar.", "Installing theme: :name": "Descar<PERSON><PERSON><PERSON> o tema: :name", "Extracting theme: :name": "Extracting theme: :name", "Seeding theme: :name": "Seeding theme: :name", "Removing plugin: :name": "Removing plugin: :name", "Please specify a Plugin name to install.": "Por favor, especifique um nome da extensão para instalar.", "Update process complete": "O processo de actualização foi realizado com sucesso.", "Package installed successfully": "A extensão foi instalada com sucesso.", "Check Dependencies": "Check Dependencies", "Install Dependencies": "Install Dependencies", "There are missing dependencies needed for the system to run correctly.": "There are missing dependencies needed for the system to run correctly.", "License Key": "License Key", "How to find your License Key": "How to find your License Key", "The password attribute is required.": "The password attribute is required.", "The login attribute is required.": "The login attribute is required.", "Cannot login user since they are not activated.": "Cannot login user since they are not activated.", "Cannot login user since they are banned.": "Cannot login user since they are banned.", "Cannot login user since they are suspended.": "Cannot login user since they are suspended.", "A user was not found with the given credentials.": "A user was not found with the given credentials.", "A user was found but the password did not match.": "A user was found but the password did not match.", "User is not logged in": "User is not logged in", "Register Software": "Register Software", "View the Dashboard": "<PERSON><PERSON>r o <PERSON>el", "Set the Default Dashboard": "Set the Default Dashboard", "Log File": "Ficheiro de registo", "Sendmail": "Sendmail", "SMTP": "SMTP", "Mailgun": "Mailgun", "SES": "SES", "Postmark": "Postmark", "No encryption": "Sem criptografia", "TLS": "TLS", "Mail Configuration": "Configurações de E-mail", "Manage email configuration.": "<PERSON><PERSON><PERSON> configuraç<PERSON>es de e-mail.", "Sender Name": "Nome do Remetente", "Sender Email": "E-mail do Remetente", "Mail Method": "<PERSON><PERSON><PERSON><PERSON>", "SMTP Address": "Endereço SMTP", "SMTP Port": "Porta SMTP", "SMTP Encryption Protocol": "Protocolo de criptografia SMTP", "SMTP Authorization Required": "Autenticação SMTP obrigatória", "Use this checkbox if your SMTP server requires authorization.": "Use esta opção se o seu servidor SMTP requer autenticação.", "Username": "Utilizador", "Password": "<PERSON><PERSON>", "Sendmail Path": "<PERSON>in<PERSON> do Sendmail", "Please specify the path of the sendmail program.": "Por favor, especifique o caminho do programa Sendmail.", "Mailgun Domain": "Domínio do <PERSON>gun", "Please specify the Mailgun domain name.": "Por favor, forneça o domínio do Mailgun.", "Mailgun Secret": "Mailgun Secret", "Enter your Mailgun API key.": "Forneça sua chave de API do Mailgun.", "SES Key": "Chave SES", "Enter your SES API key": "Forneça sua chave do SES", "SES Secret": "SES Secret", "Enter your SES API secret key": "Forneça sua chave de API do SES.", "SES Region": "Região SES", "Enter your SES region (e.g. us-east-1)": "Entre com sua região SES (exemplo: us-east-1)", "Postmark Token": "Postmark Token", "Enter your Postmark API secret key": "Enter your Postmark API secret key", "Define administrator roles": "Define administrator roles", "Restrict access to this site to only administrator with the following roles.": "Restrict access to this site to only administrator with the following roles.", "Group": "Group", "Create Group": "Create Group", "Site Group": "Site Group", "Site Groups": "Site Groups", "Manage Site Groups": "Manage Site Groups", "Group Name": "Group Name", "All Sites": "All Sites", "Manage Backend Preferences": "<PERSON><PERSON><PERSON><PERSON> da área administrativa", "Manage Code Editor Preferences": "<PERSON><PERSON><PERSON>ência<PERSON> do editor de código", "Customize Backend Styles": "<PERSON><PERSON><PERSON> o backend", "Mail Templates": "Modelos de E-mail", "Modify the mail templates that are sent to users and administrators, manage email layouts.": "Modificar os modelos dos e-mails que são enviados para utilizadores e administradores.", "Mail Branding": "Mail Branding", "Modify the colors and appearance of mail templates.": "Modify the colors and appearance of mail templates.", "Event Log": "Registo de Eventos", "View system log messages with their recorded time and details.": "Visualize as mensagens do sistema, com horário e detalhes.", "Request Log": "Registo de Requisições", "View bad or redirected requests, such as Page not found (404).": "Visualize requisições mal sucedidas na aplicação, como Página não encontrada (404).", "Log Settings": "Configurações de registo", "Specify which areas should use logging.": "Especifique que áreas devem ter registo.", "Test message sent.": "Mensagem de teste enviada com sucesso.", "New Layout": "Novo esboço", "New Partial": "New Partial", "New Template": "Novo modelo", "Template": "<PERSON><PERSON>", "Templates": "Modelos", "Layouts": "Esboços", "Partials": "Partials", "Partial": "Partial", "Mail Partials": "Mail Partials", "Layout": "Esboço", "Mail Layouts": "Esboços de e-mail", "Unique code used to refer to this template": "Código exclusivo utilizado para se referir a este modelo", "-- No layout --": "-- <PERSON><PERSON> es<PERSON> --", "HTML": "HTML", "CSS": "CSS", "Plaintext": "Texto Simples", "Disable automatic inline CSS": "Disable automatic inline CSS", "Options": "Options", "Subject": "<PERSON><PERSON><PERSON>", "Email message subject": "<PERSON><PERSON><PERSON> da mensagem", "Description": "Descrição", "Drivers Not Installed": "Drivers não instalados", "This mail method requires the plugin \":plugin\" be installed before you can send mail.": "Este método requer que a extensão \":plugin\" esteja instalada.", "ID": "ID", "Event ID": "Identificador do Evento", "Level": "Nível", "Date & Time": "Data & Hora", "Message": "Mensagem", "Logging": "Registos", "Log Bad Requests": "Registar requisições inválidas", "Browser requests that may require attention, such as 404 errors.": "Requisições que requerem a sua atenção, por exemplo erros 404.", "Log Theme Changes": "Registar alterações de tema", "When a change is made to the theme using the backend.": "Quado uma alteração é efectuada no tema utilizando o backend.", "Log System Events": "Registo de eventos de sistema", "Store system events in the database in addition to the file-based log.": "Armazenar eventos na base de dados além do registo em ficheiro.", "Background": "Background", "Body background": "Body background", "Content background": "Content background", "Inner content background": "Inner content background", "Buttons": "Buttons", "Button text color": "Button text color", "Primary button background": "Primary button background", "Positive button background": "Positive button background", "Negative button background": "Negative button background", "Typography": "Typography", "Header color": "Header color", "Headings color": "Headings color", "Text color": "Text color", "Link color": "Link color", "Footer color": "Footer color", "Borders": "Borders", "Body border color": "Body border color", "Subcopy border color": "Subcopy border color", "Table border color": "Table border color", "Components": "Components", "Panel background": "Panel background", "Promotion background": "Promotion background", "Promotion border color": "Promotion border color", "Customize Mail Appearance": "Customize Mail Appearance", "Version": "Vers<PERSON>", "Enabled": "Enabled", "Latest": "Latest", "Log ID": "ID do registo", "Counter": "<PERSON><PERSON><PERSON>", "Referers": "Referências", "URL": "URL", "This log displays a list of potential errors that occur in the application, such as exceptions and debugging information.": "Este registo mostra a lista dos potenciais erros que ocorreram na aplicação, como exceções e informações de depuração.", "Event": "Evento", "This log displays a list of browser requests that may require attention. For example, if a visitor opens a CMS page that cannot be found, a record is created with the status code 404.": "Este registro mostra uma lista de requisições que requerem atenção. Por exemplo, se um utilizador solicitar uma página não encontrada, será registado com o status 404.", "There were no detected referrers to this URL.": "There were no detected referrers to this URL.", "Request": "Requisição", "Event log emptied": "Registo de eventos esvaziado com sucesso.", "Empty Event Log": "Esvaziar registo de eventos", "Emptying Event Log...": "Esvaziando registo de eventos...", "Return to Event Log": "Regressar ao registo de eventos", "Installed": "Installed", "Primary Site": "Primary Site", "Refresh Data": "Refresh Data", "Remove Data": "Remove Data", "Are you sure you want to reset the selected plugins? This will reset each plugin's data, restoring it to the initial install state.": "Tem a certeza?", "Reset Plugin Data": "Reset Plugin Data", "Enable Plugins": "Enable Plugins", "Are you sure you want to :action these plugins?": "Are you sure you want to :action these plugins?", "enable": "enable", "disable": "disable", "Disable Plugins": "Disable Plugins", "Select Action...": "Select Action...", "Data has been removed.": "Data has been removed.", "Plugin has been removed from the file system.": "Extensão removida do sistema de ficheiros.", "search plugins to install...": "Procurar extensão para instalar...", "Plugin has been disabled by configuration.": "Plugin has been disabled by configuration.", "Plugin has missing dependencies or disabled by system.": "Plugin has missing dependencies or disabled by system.", "search themes to install...": "Procurar temas para instalar...", "Install Theme": "Instalar tema", "Theme Name": "Nome do Tema", "Install Plugin": "Instalar extensão", "Plugin Name": "Nome da extensão", "Name the plugin by its unique code. For example, RainLab.Blog": "Nomeie a extensão pelo seu código exclusivo. Por exemplo, RainLab.Blog", "Name the theme by its unique code. For example, RainLab.Vanilla": "O nome do tema deve ser único. <PERSON><PERSON> exemplo, Rain<PERSON>ab<PERSON>illa", "Select Installation Method": "Select Installation Method", "Make a Copy (Recommended)": "Make a Copy (Recommended)", "Take a copy of this theme to customize it and manually manage future updates.": "Take a copy of this theme to customize it and manually manage future updates.", "Install with Composer": "Install with Composer", "Extend the theme using a child theme to preserve future updates from the theme author.": "Extend the theme using a child theme to preserve future updates from the theme author.", "Seed Theme Data": "Seed Theme Data", "Import the blueprints files, language files and database contents for this theme, if applicable. You can skip this and do it later via the Frontend Theme settings.": "Import the blueprints files, language files and database contents for this theme, if applicable. You can skip this and do it later via the Frontend Theme settings.", "Change Status": "Change Status", "Site Definitions": "Site Definitions"}