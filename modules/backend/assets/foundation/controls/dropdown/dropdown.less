//
// Dropdown
// --------------------------------------------------

@import "dropdown.variables.less";

.dropdown-menu.backend-dropdownmenu .dropdown-container > ul,
.control-dropdown.dropdown-menu {
    padding: 0;
    list-style: none;
    background-color: @dropdown-bg;
    position: relative;
    border: @popup-border;
    box-shadow: @box-shadow-z1;
    border-radius: @border-radius-base;

    &.is-fixed {
        position: fixed !important;
    }

    &.offset-left {
        left: 10px;
    }

    > li {
        a, button {
            outline: none;
            padding: 10px 15px;
            font-size: @font-size-base;
            display: block;
            color: @dropdown-link-color;
            position: relative;
            white-space: nowrap;
            text-decoration: none;

            &:hover, &:active, &:focus:active {
                color: @dropdown-hover-color;
                background-color: @dropdown-hover-bg !important;
                i {
                    color: @dropdown-hover-color;
                }

                &[class^="oc-icon-"],
                &[class*=" oc-icon-"] {
                    &:before {
                        color: @dropdown-hover-color;
                    }
                }
            }

            &:active, &:focus:active {
                background-color: @dropdown-active-bg !important;
            }

            &[class^="oc-icon-"],
            &[class*=" oc-icon-"] {
                padding-left: 30px;

                &:before {
                    position: absolute;
                    font-size: 14px;
                    left: 9px;
                    top: 14px;
                    color: @dropdown-link-color-fade;
                }
            }

            > i {
                color: @dropdown-link-color-fade;
                font-size: 14px;
                margin-right: 4px;
                margin-left: -2px;
            }
        }

        &.first-item:not(.disabled) {
            a, button {
                &:hover, &:focus, &:active {
                    border-top-left-radius: 4px;
                    border-top-right-radius: 4px;
                }
            }
        }

        &.last-item:not(.disabled) {
            a, button {
                &:hover, &:focus, &:active {
                    border-bottom-left-radius: 4px;
                    border-bottom-right-radius: 4px;
                }
            }
        }

        &.dropdown-title { display: none; }
        &.dropdown-divider { margin: 0; }

        &.active > a {
            font-weight: bold;
        }

        &.disabled a,
        &.disabled button,
        a[disabled],
        button[disabled] {
            &, &:hover, &:active, &:focus:active {
                cursor: not-allowed;
                background-color: @dropdown-bg !important;
                color: @dropdown-link-disabled-color;
                > i {
                    color: @dropdown-link-color-fade;
                }
            }

            &[class^="oc-icon-"],
            &[class*=" oc-icon-"] {
                &:before {
                    color: @dropdown-link-color-fade;
                }
            }
        }
    }
}

// Adjustment for octo-icons
// @deprecated
// .control-dropdown.dropdown-menu > li {
//     a, button {
//         > i[class^="octo-icon-"] {
//             font-size: 16px;
//             position: relative;
//             top: 2px;
//             left: -2px;
//         }
//     }
// }

html:not(.user-touch) {
    .control-dropdown.dropdown-menu > li {
        a, button {
            &:focus {
                background: @toolbar-focus-bg;
            }
        }
    }
}

.touch .control-dropdown.dropdown-menu > li {
    a:hover {
        color: @dropdown-link-color;
        background: white;
    }
}

body.dropdown-open {
    .dropdown-overlay {
        position: fixed;
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        // background: rgba(0,0,0,0.1);
        z-index: @zindex-dropdown - 1;
    }
}

@media (max-width: @screen-sm) {
    body.dropdown-open {
        overflow: hidden;

        .dropdown-overlay {
            background: rgba(0,0,0,0.4);
        }

        .control-dropdown.dropdown-menu {
            overflow: auto;
            overflow-y: scroll;
            position: fixed !important;
            margin: 15px !important;
            top: auto !important;
            right: 0 !important;
            bottom: 0 !important;
            left: 0 !important;
            transform: none !important;
            z-index: @zindex-dropdown;
            border-radius: 8px;

            > li {
                a, button {
                    font-size: 16px;
                    // line-height: 50px;
                    padding-top: 12px;
                    padding-bottom: 12px;
                }

                &.first-item:not(.disabled) {
                    a, button {
                        &:hover, &:focus, &:active {
                            border-top-left-radius: 8px;
                            border-top-right-radius: 8px;
                        }
                    }
                }

                &.last-item:not(.disabled) {
                    a, button {
                        &:hover, &:focus, &:active {
                            border-bottom-left-radius: 8px;
                            border-bottom-right-radius: 8px;
                        }
                    }
                }
            }
        }
    }
}
