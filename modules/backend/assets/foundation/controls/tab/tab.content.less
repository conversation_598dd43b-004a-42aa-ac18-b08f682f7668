@content-tab-content-bg:           var(--oc-content-tab-content-bg);
@content-tab-content-active-bg:    @body-bg;
@content-tab-content-border:       @border-color;

:root, [data-bs-theme="light"] {
    --oc-content-tab-content-bg: #e9edf3;
}

[data-bs-theme="dark"] {
    --oc-content-tab-content-bg: #181a1e;
}

//
// Content tabs
//

.control-tabs.content-tabs {
    > ul.nav-tabs {
        position: relative;
        &:before {
            content: ' ';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            border-bottom: 1px solid @content-tab-content-border;
        }

        li {
            margin-right: 0;
            border-top: 1px solid @content-tab-content-border;
            border-right: 1px solid @content-tab-content-border;
            border-bottom: 1px solid @content-tab-content-border;
            background-color: @content-tab-content-bg;

            a {
                font-size: 1rem;
                padding: 7px 20px;
                margin: 0;
                border: none;
                // text-transform: uppercase;
                background: transparent;
                font-weight: normal;

                span.title {
                    span {
                        border-top: none;
                        padding-top: 5px;
                    }
                }
            }

            &:first-child {
                border-left: 1px solid @content-tab-content-border;
            }

            &:last-child {
                border-right: 1px solid @content-tab-content-border;
            }

            &.active {
                background: @content-tab-content-active-bg;
                border-bottom-color: transparent;
                a {
                    font-weight: 600;
                }
            }
        }
    }

    // Tab divider to sit inset the standard padding (20px)
    &.tabs-inset {
        > ul.nav-tabs, > div > ul.nav-tabs, > div > div > ul.nav-tabs {
            margin-left: -20px;
            margin-right: -20px;
            li:first-child {
                margin-left: 20px;
            }
        }
    }

    // Tabs to sit in by the standard offset (20px)
    &.tabs-offset {
        > ul.nav-tabs {
            li:first-child {
                margin-left: 20px;
            }
        }
    }

    // Tabs to sit flush to the element above
    &.tabs-flush {
        > ul.nav-tabs {
            background-color: @content-tab-content-bg;
            li {
                border-top: none;
            }
            li:last-child {
                border-right: 1px solid transparent;
            }
            li:first-child {
                border-left: 1px solid transparent;
            }
            li.active:last-child {
                border-right: 1px solid @content-tab-content-border;
            }
        }
    }

    > .tab-content > .tab-pane {
        padding-top: 0;

        div.list-header,
        div.padded-container,
        div.toolbar-widget {
            background: @content-tab-content-active-bg;
            padding-top: 20px;
        }

        // Pane to sit inset the standard padding (20px)
        &.pane-inset {
            margin-left: -20px;
            margin-right: -20px;
        }
    }
}
