!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t=t||self).Sortable=e()}(this,(function(){"use strict";function t(t,e){var n,o=Object.keys(t);return Object.getOwnPropertySymbols&&(n=Object.getOwnPropertySymbols(t),e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),o.push.apply(o,n)),o}function e(e){for(var n=1;n<arguments.length;n++){var o=null!=arguments[n]?arguments[n]:{};n%2?t(Object(o),!0).forEach((function(t){var n,i;n=e,t=o[i=t],i in n?Object.defineProperty(n,i,{value:t,enumerable:!0,configurable:!0,writable:!0}):n[i]=t})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(o)):t(Object(o)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(o,t))}))}return e}function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(){return(o=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n,o=arguments[e];for(n in o)Object.prototype.hasOwnProperty.call(o,n)&&(t[n]=o[n])}return t}).apply(this,arguments)}function i(t,e){if(null==t)return{};var n,o=function(t,e){if(null==t)return{};for(var n,o={},i=Object.keys(t),r=0;r<i.length;r++)n=i[r],0<=e.indexOf(n)||(o[n]=t[n]);return o}(t,e);if(Object.getOwnPropertySymbols)for(var i=Object.getOwnPropertySymbols(t),r=0;r<i.length;r++)n=i[r],0<=e.indexOf(n)||Object.prototype.propertyIsEnumerable.call(t,n)&&(o[n]=t[n]);return o}function r(t){return function(t){if(Array.isArray(t))return a(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return a(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Map"===(n="Object"===n&&t.constructor?t.constructor.name:n)||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?a(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function a(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function l(t){if("undefined"!=typeof window&&window.navigator)return!!navigator.userAgent.match(t)}var s=l(/(?:Trident.*rv[ :]?11\.|msie|iemobile|Windows Phone)/i),c=l(/Edge/i),u=l(/firefox/i),d=l(/safari/i)&&!l(/chrome/i)&&!l(/android/i),h=l(/iP(ad|od|hone)/i),f=l(/chrome/i)&&l(/android/i),p={capture:!1,passive:!1};function g(t,e,n){t.addEventListener(e,n,!s&&p)}function m(t,e,n){t.removeEventListener(e,n,!s&&p)}function v(t,e){if(e&&(">"===e[0]&&(e=e.substring(1)),t))try{if(t.matches)return t.matches(e);if(t.msMatchesSelector)return t.msMatchesSelector(e);if(t.webkitMatchesSelector)return t.webkitMatchesSelector(e)}catch(t){return}}function b(t,e,n,o){if(t){n=n||document;do{if(null!=e&&(">"!==e[0]||t.parentNode===n)&&v(t,e)||o&&t===n)return t}while(t!==n&&(t=(i=t).host&&i!==document&&i.host.nodeType?i.host:i.parentNode))}var i;return null}var y,w=/\s+/g;function E(t,e,n){var o;t&&e&&(t.classList?t.classList[n?"add":"remove"](e):(o=(" "+t.className+" ").replace(w," ").replace(" "+e+" "," "),t.className=(o+(n?" "+e:"")).replace(w," ")))}function D(t,e,n){var o=t&&t.style;if(o){if(void 0===n)return document.defaultView&&document.defaultView.getComputedStyle?n=document.defaultView.getComputedStyle(t,""):t.currentStyle&&(n=t.currentStyle),void 0===e?n:n[e];o[e=e in o||-1!==e.indexOf("webkit")?e:"-webkit-"+e]=n+("string"==typeof n?"":"px")}}function S(t,e){var n="";if("string"==typeof t)n=t;else do{var o=D(t,"transform")}while(o&&"none"!==o&&(n=o+" "+n),!e&&(t=t.parentNode));var i=window.DOMMatrix||window.WebKitCSSMatrix||window.CSSMatrix||window.MSCSSMatrix;return i&&new i(n)}function _(t,e,n){if(t){var o=t.getElementsByTagName(e),i=0,r=o.length;if(n)for(;i<r;i++)n(o[i],i);return o}return[]}function C(){return document.scrollingElement||document.documentElement}function T(t,e,n,o,i){if(t.getBoundingClientRect||t===window){var r,a,l,c,u,d,h=t!==window&&t.parentNode&&t!==C()?(a=(r=t.getBoundingClientRect()).top,l=r.left,c=r.bottom,u=r.right,d=r.height,r.width):(l=a=0,c=window.innerHeight,u=window.innerWidth,d=window.innerHeight,window.innerWidth);if((e||n)&&t!==window&&(i=i||t.parentNode,!s))do{if(i&&i.getBoundingClientRect&&("none"!==D(i,"transform")||n&&"static"!==D(i,"position"))){var f=i.getBoundingClientRect();a-=f.top+parseInt(D(i,"border-top-width")),l-=f.left+parseInt(D(i,"border-left-width")),c=a+r.height,u=l+r.width;break}}while(i=i.parentNode);return o&&t!==window&&(o=(e=S(i||t))&&e.a,t=e&&e.d,e&&(c=(a/=t)+(d/=t),u=(l/=o)+(h/=o))),{top:a,left:l,bottom:c,right:u,width:h,height:d}}}function x(t,e,n){for(var o=I(t,!0),i=T(t)[e];o;){var r=T(o)[n];if(!("top"===n||"left"===n?r<=i:i<=r))return o;if(o===C())break;o=I(o,!1)}return!1}function O(t,e,n,o){for(var i=0,r=0,a=t.children;r<a.length;){if("none"!==a[r].style.display&&a[r]!==Yt.ghost&&(o||a[r]!==Yt.dragged)&&b(a[r],n.draggable,t,!1)){if(i===e)return a[r];i++}r++}return null}function A(t,e){for(var n=t.lastElementChild;n&&(n===Yt.ghost||"none"===D(n,"display")||e&&!v(n,e));)n=n.previousElementSibling;return n||null}function M(t,e){var n=0;if(!t||!t.parentNode)return-1;for(;t=t.previousElementSibling;)"TEMPLATE"===t.nodeName.toUpperCase()||t===Yt.clone||e&&!v(t,e)||n++;return n}function N(t){var e=0,n=0,o=C();if(t)do{var i=(r=S(t)).a,r=r.d}while(e+=t.scrollLeft*i,n+=t.scrollTop*r,t!==o&&(t=t.parentNode));return[e,n]}function I(t,e){if(!t||!t.getBoundingClientRect)return C();var n=t,o=!1;do{if(n.clientWidth<n.scrollWidth||n.clientHeight<n.scrollHeight){var i=D(n);if(n.clientWidth<n.scrollWidth&&("auto"==i.overflowX||"scroll"==i.overflowX)||n.clientHeight<n.scrollHeight&&("auto"==i.overflowY||"scroll"==i.overflowY)){if(!n.getBoundingClientRect||n===document.body)return C();if(o||e)return n;o=!0}}}while(n=n.parentNode);return C()}function P(t,e){return Math.round(t.top)===Math.round(e.top)&&Math.round(t.left)===Math.round(e.left)&&Math.round(t.height)===Math.round(e.height)&&Math.round(t.width)===Math.round(e.width)}function k(t,e){return function(){var n;y||(1===(n=arguments).length?t.call(this,n[0]):t.apply(this,n),y=setTimeout((function(){y=void 0}),e))}}function R(t,e,n){t.scrollLeft+=e,t.scrollTop+=n}function X(t){var e=window.Polymer,n=window.jQuery||window.Zepto;return e&&e.dom?e.dom(t).cloneNode(!0):n?n(t).clone(!0)[0]:t.cloneNode(!0)}function Y(t,e){D(t,"position","absolute"),D(t,"top",e.top),D(t,"left",e.left),D(t,"width",e.width),D(t,"height",e.height)}function B(t){D(t,"position",""),D(t,"top",""),D(t,"left",""),D(t,"width",""),D(t,"height","")}var F="Sortable"+(new Date).getTime();var j=[],H={initializeByDefault:!0},L={mount:function(t){for(var e in H)!H.hasOwnProperty(e)||e in t||(t[e]=H[e]);j.forEach((function(e){if(e.pluginName===t.pluginName)throw"Sortable: Cannot mount plugin ".concat(t.pluginName," more than once")})),j.push(t)},pluginEvent:function(t,n,o){var i=this;this.eventCanceled=!1,o.cancel=function(){i.eventCanceled=!0};var r=t+"Global";j.forEach((function(i){n[i.pluginName]&&(n[i.pluginName][r]&&n[i.pluginName][r](e({sortable:n},o)),n.options[i.pluginName]&&n[i.pluginName][t]&&n[i.pluginName][t](e({sortable:n},o)))}))},initializePlugins:function(t,e,n,i){for(var r in j.forEach((function(i){var r=i.pluginName;(t.options[r]||i.initializeByDefault)&&((i=new i(t,e,t.options)).sortable=t,i.options=t.options,t[r]=i,o(n,i.defaults))})),t.options){var a;t.options.hasOwnProperty(r)&&void 0!==(a=this.modifyOption(t,r,t.options[r]))&&(t.options[r]=a)}},getEventProperties:function(t,e){var n={};return j.forEach((function(i){"function"==typeof i.eventProperties&&o(n,i.eventProperties.call(e[i.pluginName],t))})),n},modifyOption:function(t,e,n){var o;return j.forEach((function(i){t[i.pluginName]&&i.optionListeners&&"function"==typeof i.optionListeners[e]&&(o=i.optionListeners[e].call(t[i.pluginName],n))})),o}};function K(t){var n=t.sortable,o=t.rootEl,i=t.name,r=t.targetEl,a=t.cloneEl,l=t.toEl,u=t.fromEl,d=t.oldIndex,h=t.newIndex,f=t.oldDraggableIndex,p=t.newDraggableIndex,g=t.originalEvent,m=t.putSortable,v=t.extraEventProperties;if(n=n||o&&o[F]){var b,y=n.options;t="on"+i.charAt(0).toUpperCase()+i.substr(1);!window.CustomEvent||s||c?(b=document.createEvent("Event")).initEvent(i,!0,!0):b=new CustomEvent(i,{bubbles:!0,cancelable:!0}),b.to=l||o,b.from=u||o,b.item=r||o,b.clone=a,b.oldIndex=d,b.newIndex=h,b.oldDraggableIndex=f,b.newDraggableIndex=p,b.originalEvent=g,b.pullMode=m?m.lastPutMode:void 0;var w,E=e(e({},v),L.getEventProperties(i,n));for(w in E)b[w]=E[w];o&&o.dispatchEvent(b),y[t]&&y[t].call(n,b)}}function W(t,n){var o=(r=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{}).evt,r=i(r,z);L.pluginEvent.bind(Yt)(t,n,e({dragEl:U,parentEl:q,ghostEl:V,rootEl:Z,nextEl:$,lastDownEl:Q,cloneEl:J,cloneHidden:tt,dragStarted:ft,putSortable:at,activeSortable:Yt.active,originalEvent:o,oldIndex:et,oldDraggableIndex:ot,newIndex:nt,newDraggableIndex:it,hideGhostForTarget:Pt,unhideGhostForTarget:kt,cloneNowHidden:function(){tt=!0},cloneNowShown:function(){tt=!1},dispatchSortableEvent:function(t){G({sortable:n,name:t,originalEvent:o})}},r))}var z=["evt"];function G(t){K(e({putSortable:at,cloneEl:J,targetEl:U,rootEl:Z,oldIndex:et,oldDraggableIndex:ot,newIndex:nt,newDraggableIndex:it},t))}var U,q,V,Z,$,Q,J,tt,et,nt,ot,it,rt,at,lt,st,ct,ut,dt,ht,ft,pt,gt,mt,vt,bt=!1,yt=!1,wt=[],Et=!1,Dt=!1,St=[],_t=!1,Ct=[],Tt="undefined"!=typeof document,xt=h,Ot=c||s?"cssFloat":"float",At=Tt&&!f&&!h&&"draggable"in document.createElement("div"),Mt=function(){if(Tt){if(s)return!1;var t=document.createElement("x");return t.style.cssText="pointer-events:auto","auto"===t.style.pointerEvents}}(),Nt=function(t,e){var n=D(t),o=parseInt(n.width)-parseInt(n.paddingLeft)-parseInt(n.paddingRight)-parseInt(n.borderLeftWidth)-parseInt(n.borderRightWidth),i=O(t,0,e),r=O(t,1,e),a=i&&D(i),l=r&&D(r),s=a&&parseInt(a.marginLeft)+parseInt(a.marginRight)+T(i).width;t=l&&parseInt(l.marginLeft)+parseInt(l.marginRight)+T(r).width;return"flex"===n.display?"column"===n.flexDirection||"column-reverse"===n.flexDirection?"vertical":"horizontal":"grid"===n.display?n.gridTemplateColumns.split(" ").length<=1?"vertical":"horizontal":i&&a.float&&"none"!==a.float?(e="left"===a.float?"left":"right",!r||"both"!==l.clear&&l.clear!==e?"horizontal":"vertical"):i&&("block"===a.display||"flex"===a.display||"table"===a.display||"grid"===a.display||o<=s&&"none"===n[Ot]||r&&"none"===n[Ot]&&o<s+t)?"vertical":"horizontal"},It=function(t){function e(t,n){return function(o,i,r,a){var l=o.options.group.name&&i.options.group.name&&o.options.group.name===i.options.group.name;return!(null!=t||!n&&!l)||null!=t&&!1!==t&&(n&&"clone"===t?t:"function"==typeof t?e(t(o,i,r,a),n)(o,i,r,a):(i=(n?o:i).options.group.name,!0===t||"string"==typeof t&&t===i||t.join&&-1<t.indexOf(i)))}}var o={},i=t.group;i&&"object"==n(i)||(i={name:i}),o.name=i.name,o.checkPull=e(i.pull,!0),o.checkPut=e(i.put),o.revertClone=i.revertClone,t.group=o},Pt=function(){!Mt&&V&&D(V,"display","none")},kt=function(){!Mt&&V&&D(V,"display","")};function Rt(t){if(U){t=t.touches?t.touches[0]:t;var e=(i=t.clientX,r=t.clientY,wt.some((function(t){if((o=t[F].options.emptyInsertThreshold)&&!A(t)){var e=T(t),n=i>=e.left-o&&i<=e.right+o,o=r>=e.top-o&&r<=e.bottom+o;return n&&o?a=t:void 0}})),a);if(e){var n,o={};for(n in t)t.hasOwnProperty(n)&&(o[n]=t[n]);o.target=o.rootEl=e,o.preventDefault=void 0,o.stopPropagation=void 0,e[F]._onDragOver(o)}}var i,r,a}function Xt(t){U&&U.parentNode[F]._isOutsideThisEl(t.target)}function Yt(t,n){if(!t||!t.nodeType||1!==t.nodeType)throw"Sortable: `el` must be an HTMLElement, not ".concat({}.toString.call(t));this.el=t,this.options=n=o({},n),t[F]=this;var i,r,a={group:null,sort:!0,disabled:!1,store:null,handle:null,draggable:/^[uo]l$/i.test(t.nodeName)?">li":">*",swapThreshold:1,invertSwap:!1,invertedSwapThreshold:null,removeCloneOnHide:!0,direction:function(){return Nt(t,this.options)},ghostClass:"sortable-ghost",chosenClass:"sortable-chosen",dragClass:"sortable-drag",ignore:"a, img",filter:null,preventOnFilter:!0,animation:0,easing:null,setData:function(t,e){t.setData("Text",e.textContent)},dropBubble:!1,dragoverBubble:!1,dataIdAttr:"data-id",delay:0,delayOnTouchOnly:!1,touchStartThreshold:(Number.parseInt?Number:window).parseInt(window.devicePixelRatio,10)||1,forceFallback:!1,fallbackClass:"sortable-fallback",fallbackOnBody:!1,fallbackTolerance:0,fallbackOffset:{x:0,y:0},supportPointer:!1!==Yt.supportPointer&&"PointerEvent"in window&&!d,emptyInsertThreshold:5};for(i in L.initializePlugins(this,t,a),a)i in n||(n[i]=a[i]);for(r in It(n),this)"_"===r.charAt(0)&&"function"==typeof this[r]&&(this[r]=this[r].bind(this));this.nativeDraggable=!n.forceFallback&&At,this.nativeDraggable&&(this.options.touchStartThreshold=1),n.supportPointer?g(t,"pointerdown",this._onTapStart):(g(t,"mousedown",this._onTapStart),g(t,"touchstart",this._onTapStart)),this.nativeDraggable&&(g(t,"dragover",this),g(t,"dragenter",this)),wt.push(this.el),n.store&&n.store.get&&this.sort(n.store.get(this)||[]),o(this,function(){var t,n=[];return{captureAnimationState:function(){n=[],this.options.animation&&[].slice.call(this.el.children).forEach((function(t){var o,i;"none"!==D(t,"display")&&t!==Yt.ghost&&(n.push({target:t,rect:T(t)}),o=e({},n[n.length-1].rect),!t.thisAnimationDuration||(i=S(t,!0))&&(o.top-=i.f,o.left-=i.e),t.fromRect=o)}))},addAnimationState:function(t){n.push(t)},removeAnimationState:function(t){n.splice(function(t,e){for(var n in t)if(t.hasOwnProperty(n))for(var o in e)if(e.hasOwnProperty(o)&&e[o]===t[n][o])return Number(n);return-1}(n,{target:t}),1)},animateAll:function(e){var o=this;if(!this.options.animation)return clearTimeout(t),void("function"==typeof e&&e());var i=!1,r=0;n.forEach((function(t){var e=0,n=t.target,a=n.fromRect,l=T(n),s=n.prevFromRect,c=n.prevToRect,u=t.rect,d=S(n,!0);d&&(l.top-=d.f,l.left-=d.e),n.toRect=l,n.thisAnimationDuration&&P(s,l)&&!P(a,l)&&(u.top-l.top)/(u.left-l.left)==(a.top-l.top)/(a.left-l.left)&&(t=u,d=s,s=c,c=o.options,e=Math.sqrt(Math.pow(d.top-t.top,2)+Math.pow(d.left-t.left,2))/Math.sqrt(Math.pow(d.top-s.top,2)+Math.pow(d.left-s.left,2))*c.animation),P(l,a)||(n.prevFromRect=a,n.prevToRect=l,e=e||o.options.animation,o.animate(n,u,l,e)),e&&(i=!0,r=Math.max(r,e),clearTimeout(n.animationResetTimer),n.animationResetTimer=setTimeout((function(){n.animationTime=0,n.prevFromRect=null,n.fromRect=null,n.prevToRect=null,n.thisAnimationDuration=null}),e),n.thisAnimationDuration=e)})),clearTimeout(t),i?t=setTimeout((function(){"function"==typeof e&&e()}),r):"function"==typeof e&&e(),n=[]},animate:function(t,e,n,o){var i,r;o&&(D(t,"transition",""),D(t,"transform",""),i=(r=S(this.el))&&r.a,r=r&&r.d,i=(e.left-n.left)/(i||1),r=(e.top-n.top)/(r||1),t.animatingX=!!i,t.animatingY=!!r,D(t,"transform","translate3d("+i+"px,"+r+"px,0)"),this.forRepaintDummy=t.offsetWidth,D(t,"transition","transform "+o+"ms"+(this.options.easing?" "+this.options.easing:"")),D(t,"transform","translate3d(0,0,0)"),"number"==typeof t.animated&&clearTimeout(t.animated),t.animated=setTimeout((function(){D(t,"transition",""),D(t,"transform",""),t.animated=!1,t.animatingX=!1,t.animatingY=!1}),o))}}}())}function Bt(t,e,n,o,i,r,a,l){var u,d,h=t[F],f=h.options.onMove;return!window.CustomEvent||s||c?(u=document.createEvent("Event")).initEvent("move",!0,!0):u=new CustomEvent("move",{bubbles:!0,cancelable:!0}),u.to=e,u.from=t,u.dragged=n,u.draggedRect=o,u.related=i||e,u.relatedRect=r||T(e),u.willInsertAfter=l,u.originalEvent=a,t.dispatchEvent(u),f?f.call(h,u,a):d}function Ft(t){t.draggable=!1}function jt(){_t=!1}function Ht(t){return setTimeout(t,0)}function Lt(t){return clearTimeout(t)}Tt&&!f&&document.addEventListener("click",(function(t){if(yt)return t.preventDefault(),t.stopPropagation&&t.stopPropagation(),t.stopImmediatePropagation&&t.stopImmediatePropagation(),yt=!1}),!0),Yt.prototype={constructor:Yt,_isOutsideThisEl:function(t){this.el.contains(t)||t===this.el||(pt=null)},_getDirection:function(t,e){return"function"==typeof this.options.direction?this.options.direction.call(this,t,e,U):this.options.direction},_onTapStart:function(t){if(t.cancelable){var e=this,n=this.el,o=this.options,i=o.preventOnFilter,r=t.type,a=t.touches&&t.touches[0]||t.pointerType&&"touch"===t.pointerType&&t,l=(a||t).target,s=t.target.shadowRoot&&(t.path&&t.path[0]||t.composedPath&&t.composedPath()[0])||l,c=o.filter;if(function(t){Ct.length=0;for(var e=t.getElementsByTagName("input"),n=e.length;n--;){var o=e[n];o.checked&&Ct.push(o)}}(n),!U&&!(/mousedown|pointerdown/.test(r)&&0!==t.button||o.disabled)&&!s.isContentEditable&&(this.nativeDraggable||!d||!l||"SELECT"!==l.tagName.toUpperCase())&&!((l=b(l,o.draggable,n,!1))&&l.animated||Q===l)){if(et=M(l),ot=M(l,o.draggable),"function"==typeof c){if(c.call(this,t,l,this))return G({sortable:e,rootEl:s,name:"filter",targetEl:l,toEl:n,fromEl:n}),W("filter",e,{evt:t}),void(i&&t.cancelable&&t.preventDefault())}else if(c=c&&c.split(",").some((function(o){if(o=b(s,o.trim(),n,!1))return G({sortable:e,rootEl:o,name:"filter",targetEl:l,fromEl:n,toEl:n}),W("filter",e,{evt:t}),!0})))return void(i&&t.cancelable&&t.preventDefault());o.handle&&!b(s,o.handle,n,!1)||this._prepareDragStart(t,a,l)}}},_prepareDragStart:function(t,e,n){var o,i=this,r=i.el,a=i.options,l=r.ownerDocument;n&&!U&&n.parentNode===r&&(o=T(n),Z=r,q=(U=n).parentNode,$=U.nextSibling,Q=n,rt=a.group,lt={target:Yt.dragged=U,clientX:(e||t).clientX,clientY:(e||t).clientY},dt=lt.clientX-o.left,ht=lt.clientY-o.top,this._lastX=(e||t).clientX,this._lastY=(e||t).clientY,U.style["will-change"]="all",o=function(){W("delayEnded",i,{evt:t}),Yt.eventCanceled?i._onDrop():(i._disableDelayedDragEvents(),!u&&i.nativeDraggable&&(U.draggable=!0),i._triggerDragStart(t,e),G({sortable:i,name:"choose",originalEvent:t}),E(U,a.chosenClass,!0))},a.ignore.split(",").forEach((function(t){_(U,t.trim(),Ft)})),g(l,"dragover",Rt),g(l,"mousemove",Rt),g(l,"touchmove",Rt),g(l,"mouseup",i._onDrop),g(l,"touchend",i._onDrop),g(l,"touchcancel",i._onDrop),u&&this.nativeDraggable&&(this.options.touchStartThreshold=4,U.draggable=!0),W("delayStart",this,{evt:t}),!a.delay||a.delayOnTouchOnly&&!e||this.nativeDraggable&&(c||s)?o():Yt.eventCanceled?this._onDrop():(g(l,"mouseup",i._disableDelayedDrag),g(l,"touchend",i._disableDelayedDrag),g(l,"touchcancel",i._disableDelayedDrag),g(l,"mousemove",i._delayedDragTouchMoveHandler),g(l,"touchmove",i._delayedDragTouchMoveHandler),a.supportPointer&&g(l,"pointermove",i._delayedDragTouchMoveHandler),i._dragStartTimer=setTimeout(o,a.delay)))},_delayedDragTouchMoveHandler:function(t){t=t.touches?t.touches[0]:t,Math.max(Math.abs(t.clientX-this._lastX),Math.abs(t.clientY-this._lastY))>=Math.floor(this.options.touchStartThreshold/(this.nativeDraggable&&window.devicePixelRatio||1))&&this._disableDelayedDrag()},_disableDelayedDrag:function(){U&&Ft(U),clearTimeout(this._dragStartTimer),this._disableDelayedDragEvents()},_disableDelayedDragEvents:function(){var t=this.el.ownerDocument;m(t,"mouseup",this._disableDelayedDrag),m(t,"touchend",this._disableDelayedDrag),m(t,"touchcancel",this._disableDelayedDrag),m(t,"mousemove",this._delayedDragTouchMoveHandler),m(t,"touchmove",this._delayedDragTouchMoveHandler),m(t,"pointermove",this._delayedDragTouchMoveHandler)},_triggerDragStart:function(t,e){e=e||"touch"==t.pointerType&&t,!this.nativeDraggable||e?this.options.supportPointer?g(document,"pointermove",this._onTouchMove):g(document,e?"touchmove":"mousemove",this._onTouchMove):(g(U,"dragend",this),g(Z,"dragstart",this._onDragStart));try{document.selection?Ht((function(){document.selection.empty()})):window.getSelection().removeAllRanges()}catch(t){}},_dragStarted:function(t,e){var n;bt=!1,Z&&U?(W("dragStarted",this,{evt:e}),this.nativeDraggable&&g(document,"dragover",Xt),n=this.options,t||E(U,n.dragClass,!1),E(U,n.ghostClass,!0),Yt.active=this,t&&this._appendGhost(),G({sortable:this,name:"start",originalEvent:e})):this._nulling()},_emulateDragOver:function(){if(st){this._lastX=st.clientX,this._lastY=st.clientY,Pt();for(var t=document.elementFromPoint(st.clientX,st.clientY),e=t;t&&t.shadowRoot&&(t=t.shadowRoot.elementFromPoint(st.clientX,st.clientY))!==e;)e=t;if(U.parentNode[F]._isOutsideThisEl(t),e)do{if(e[F]&&e[F]._onDragOver({clientX:st.clientX,clientY:st.clientY,target:t,rootEl:e})&&!this.options.dragoverBubble)break}while(e=(t=e).parentNode);kt()}},_onTouchMove:function(t){if(lt){var e=(l=this.options).fallbackTolerance,n=l.fallbackOffset,o=t.touches?t.touches[0]:t,i=V&&S(V,!0),r=V&&i&&i.a,a=V&&i&&i.d,l=xt&&vt&&N(vt);r=(o.clientX-lt.clientX+n.x)/(r||1)+(l?l[0]-St[0]:0)/(r||1),a=(o.clientY-lt.clientY+n.y)/(a||1)+(l?l[1]-St[1]:0)/(a||1);if(!Yt.active&&!bt){if(e&&Math.max(Math.abs(o.clientX-this._lastX),Math.abs(o.clientY-this._lastY))<e)return;this._onDragStart(t,!0)}V&&(i?(i.e+=r-(ct||0),i.f+=a-(ut||0)):i={a:1,b:0,c:0,d:1,e:r,f:a},i="matrix(".concat(i.a,",").concat(i.b,",").concat(i.c,",").concat(i.d,",").concat(i.e,",").concat(i.f,")"),D(V,"webkitTransform",i),D(V,"mozTransform",i),D(V,"msTransform",i),D(V,"transform",i),ct=r,ut=a,st=o),t.cancelable&&t.preventDefault()}},_appendGhost:function(){if(!V){var t=this.options.fallbackOnBody?document.body:Z,e=T(U,!0,xt,!0,t),n=this.options;if(xt){for(vt=t;"static"===D(vt,"position")&&"none"===D(vt,"transform")&&vt!==document;)vt=vt.parentNode;vt!==document.body&&vt!==document.documentElement?(vt===document&&(vt=C()),e.top+=vt.scrollTop,e.left+=vt.scrollLeft):vt=C(),St=N(vt)}E(V=U.cloneNode(!0),n.ghostClass,!1),E(V,n.fallbackClass,!0),E(V,n.dragClass,!0),D(V,"transition",""),D(V,"transform",""),D(V,"box-sizing","border-box"),D(V,"margin",0),D(V,"top",e.top),D(V,"left",e.left),D(V,"width",e.width),D(V,"height",e.height),D(V,"opacity","0.8"),D(V,"position",xt?"absolute":"fixed"),D(V,"zIndex","100000"),D(V,"pointerEvents","none"),Yt.ghost=V,t.appendChild(V),D(V,"transform-origin",dt/parseInt(V.style.width)*100+"% "+ht/parseInt(V.style.height)*100+"%")}},_onDragStart:function(t,e){var n=this,o=t.dataTransfer,i=n.options;W("dragStart",this,{evt:t}),Yt.eventCanceled?this._onDrop():(W("setupClone",this),Yt.eventCanceled||((J=X(U)).removeAttribute("id"),J.draggable=!1,J.style["will-change"]="",this._hideClone(),E(J,this.options.chosenClass,!1),Yt.clone=J),n.cloneId=Ht((function(){W("clone",n),Yt.eventCanceled||(n.options.removeCloneOnHide||Z.insertBefore(J,U),n._hideClone(),G({sortable:n,name:"clone"}))})),e||E(U,i.dragClass,!0),e?(yt=!0,n._loopId=setInterval(n._emulateDragOver,50)):(m(document,"mouseup",n._onDrop),m(document,"touchend",n._onDrop),m(document,"touchcancel",n._onDrop),o&&(o.effectAllowed="move",i.setData&&i.setData.call(n,o,U)),g(document,"drop",n),D(U,"transform","translateZ(0)")),bt=!0,n._dragStartId=Ht(n._dragStarted.bind(n,e,t)),g(document,"selectstart",n),ft=!0,d&&D(document.body,"user-select","none"))},_onDragOver:function(t){var n,o,i,r,a=this.el,l=t.target,s=this.options,c=s.group,u=Yt.active,d=rt===c,h=s.sort,f=at||u,p=this,g=!1;if(!_t){if(void 0!==t.preventDefault&&t.cancelable&&t.preventDefault(),l=b(l,s.draggable,a,!0),X("dragOver"),Yt.eventCanceled)return g;if(U.contains(t.target)||l.animated&&l.animatingX&&l.animatingY||p._ignoreWhileAnimating===l)return B(!1);if(yt=!1,u&&!s.disabled&&(d?h||(o=q!==Z):at===this||(this.lastPutMode=rt.checkPull(this,u,U,t))&&c.checkPut(this,u,U,t))){if(i="vertical"===this._getDirection(t,l),n=T(U),X("dragOverValid"),Yt.eventCanceled)return g;if(o)return q=Z,Y(),this._hideClone(),X("revert"),Yt.eventCanceled||($?Z.insertBefore(U,$):Z.appendChild(U)),B(!0);if(!(w=A(a,s.draggable))||function(t,e,n){return n=T(A(n.el,n.options.draggable)),e?t.clientX>n.right+10||t.clientX<=n.right&&t.clientY>n.bottom&&t.clientX>=n.left:t.clientX>n.right&&t.clientY>n.top||t.clientX<=n.right&&t.clientY>n.bottom+10}(t,i,this)&&!w.animated){if(w===U)return B(!1);if((l=w&&a===t.target?w:l)&&(S=T(l)),!1!==Bt(Z,a,U,n,l,S,t,!!l))return Y(),w&&w.nextSibling?a.insertBefore(U,w.nextSibling):a.appendChild(U),q=a,j(),B(!0)}else if(w&&function(t,e,n){return n=T(O(n.el,0,n.options,!0)),e?t.clientX<n.left-10||t.clientY<n.top&&t.clientX<n.right:t.clientY<n.top-10||t.clientY<n.bottom&&t.clientX<n.left}(t,i,this)){if((I=O(a,0,s,!0))===U)return B(!1);if(S=T(l=I),!1!==Bt(Z,a,U,n,l,S,t,!1))return Y(),a.insertBefore(U,I),q=a,j(),B(!0)}else if(l.parentNode===a){var m,v,y,w,S=T(l),_=U.parentNode!==a,C=(C=U.animated&&U.toRect||n,k=l.animated&&l.toRect||S,N=(r=i)?C.left:C.top,c=r?C.right:C.bottom,w=r?C.width:C.height,I=r?k.left:k.top,C=r?k.right:k.bottom,k=r?k.width:k.height,!(N===I||c===C||N+w/2===I+k/2)),N=i?"top":"left",I=(w=x(l,"top","top")||x(U,"top","top"))?w.scrollTop:void 0;if(pt!==l&&(v=S[N],Et=!1,Dt=!C&&s.invertSwap||_),0!==(m=function(t,e,n,o,i,r,a,l){var s=o?t.clientY:t.clientX,c=o?n.height:n.width;t=o?n.top:n.left,o=o?n.bottom:n.right,n=!1;if(!a)if(l&&mt<c*i){if(Et=!Et&&(1===gt?t+c*r/2<s:s<o-c*r/2)||Et)n=!0;else if(1===gt?s<t+mt:o-mt<s)return-gt}else if(t+c*(1-i)/2<s&&s<o-c*(1-i)/2)return function(t){return M(U)<M(t)?1:-1}(e);return(n=n||a)&&(s<t+c*r/2||o-c*r/2<s)?t+c/2<s?1:-1:0}(t,l,S,i,C?1:s.swapThreshold,null==s.invertedSwapThreshold?s.swapThreshold:s.invertedSwapThreshold,Dt,pt===l)))for(var P=M(U);(y=q.children[P-=m])&&("none"===D(y,"display")||y===V););if(0===m||y===l)return B(!1);gt=m;var k=(pt=l).nextElementSibling;_=!1;if(!1!==(C=Bt(Z,a,U,n,l,S,t,_=1===m)))return 1!==C&&-1!==C||(_=1===C),_t=!0,setTimeout(jt,30),Y(),_&&!k?a.appendChild(U):l.parentNode.insertBefore(U,_?k:l),w&&R(w,0,I-w.scrollTop),q=U.parentNode,void 0===v||Dt||(mt=Math.abs(v-T(l)[N])),j(),B(!0)}if(a.contains(U))return B(!1)}return!1}function X(r,s){W(r,p,e({evt:t,isOwner:d,axis:i?"vertical":"horizontal",revert:o,dragRect:n,targetRect:S,canSort:h,fromSortable:f,target:l,completed:B,onMove:function(e,o){return Bt(Z,a,U,n,e,T(e),t,o)},changed:j},s))}function Y(){X("dragOverAnimationCapture"),p.captureAnimationState(),p!==f&&f.captureAnimationState()}function B(e){return X("dragOverCompleted",{insertion:e}),e&&(d?u._hideClone():u._showClone(p),p!==f&&(E(U,(at||u).options.ghostClass,!1),E(U,s.ghostClass,!0)),at!==p&&p!==Yt.active?at=p:p===Yt.active&&at&&(at=null),f===p&&(p._ignoreWhileAnimating=l),p.animateAll((function(){X("dragOverAnimationComplete"),p._ignoreWhileAnimating=null})),p!==f&&(f.animateAll(),f._ignoreWhileAnimating=null)),(l===U&&!U.animated||l===a&&!l.animated)&&(pt=null),s.dragoverBubble||t.rootEl||l===document||(U.parentNode[F]._isOutsideThisEl(t.target),e||Rt(t)),!s.dragoverBubble&&t.stopPropagation&&t.stopPropagation(),g=!0}function j(){nt=M(U),it=M(U,s.draggable),G({sortable:p,name:"change",toEl:a,newIndex:nt,newDraggableIndex:it,originalEvent:t})}},_ignoreWhileAnimating:null,_offMoveEvents:function(){m(document,"mousemove",this._onTouchMove),m(document,"touchmove",this._onTouchMove),m(document,"pointermove",this._onTouchMove),m(document,"dragover",Rt),m(document,"mousemove",Rt),m(document,"touchmove",Rt)},_offUpEvents:function(){var t=this.el.ownerDocument;m(t,"mouseup",this._onDrop),m(t,"touchend",this._onDrop),m(t,"pointerup",this._onDrop),m(t,"touchcancel",this._onDrop),m(document,"selectstart",this)},_onDrop:function(t){var e=this.el,n=this.options;nt=M(U),it=M(U,n.draggable),W("drop",this,{evt:t}),q=U&&U.parentNode,nt=M(U),it=M(U,n.draggable),Yt.eventCanceled||(Et=Dt=bt=!1,clearInterval(this._loopId),clearTimeout(this._dragStartTimer),Lt(this.cloneId),Lt(this._dragStartId),this.nativeDraggable&&(m(document,"drop",this),m(e,"dragstart",this._onDragStart)),this._offMoveEvents(),this._offUpEvents(),d&&D(document.body,"user-select",""),D(U,"transform",""),t&&(ft&&(t.cancelable&&t.preventDefault(),n.dropBubble||t.stopPropagation()),V&&V.parentNode&&V.parentNode.removeChild(V),(Z===q||at&&"clone"!==at.lastPutMode)&&J&&J.parentNode&&J.parentNode.removeChild(J),U&&(this.nativeDraggable&&m(U,"dragend",this),Ft(U),U.style["will-change"]="",ft&&!bt&&E(U,(at||this).options.ghostClass,!1),E(U,this.options.chosenClass,!1),G({sortable:this,name:"unchoose",toEl:q,newIndex:null,newDraggableIndex:null,originalEvent:t}),Z!==q?(0<=nt&&(G({rootEl:q,name:"add",toEl:q,fromEl:Z,originalEvent:t}),G({sortable:this,name:"remove",toEl:q,originalEvent:t}),G({rootEl:q,name:"sort",toEl:q,fromEl:Z,originalEvent:t}),G({sortable:this,name:"sort",toEl:q,originalEvent:t})),at&&at.save()):nt!==et&&0<=nt&&(G({sortable:this,name:"update",toEl:q,originalEvent:t}),G({sortable:this,name:"sort",toEl:q,originalEvent:t})),Yt.active&&(null!=nt&&-1!==nt||(nt=et,it=ot),G({sortable:this,name:"end",toEl:q,originalEvent:t}),this.save())))),this._nulling()},_nulling:function(){W("nulling",this),Z=U=q=V=$=J=Q=tt=lt=st=ft=nt=it=et=ot=pt=gt=at=rt=Yt.dragged=Yt.ghost=Yt.clone=Yt.active=null,Ct.forEach((function(t){t.checked=!0})),Ct.length=ct=ut=0},handleEvent:function(t){switch(t.type){case"drop":case"dragend":this._onDrop(t);break;case"dragenter":case"dragover":U&&(this._onDragOver(t),function(t){t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.cancelable&&t.preventDefault()}(t));break;case"selectstart":t.preventDefault()}},toArray:function(){for(var t,e=[],n=this.el.children,o=0,i=n.length,r=this.options;o<i;o++)b(t=n[o],r.draggable,this.el,!1)&&e.push(t.getAttribute(r.dataIdAttr)||function(t){for(var e=t.tagName+t.className+t.src+t.href+t.textContent,n=e.length,o=0;n--;)o+=e.charCodeAt(n);return o.toString(36)}(t));return e},sort:function(t,e){var n={},o=this.el;this.toArray().forEach((function(t,e){b(e=o.children[e],this.options.draggable,o,!1)&&(n[t]=e)}),this),e&&this.captureAnimationState(),t.forEach((function(t){n[t]&&(o.removeChild(n[t]),o.appendChild(n[t]))})),e&&this.animateAll()},save:function(){var t=this.options.store;t&&t.set&&t.set(this)},closest:function(t,e){return b(t,e||this.options.draggable,this.el,!1)},option:function(t,e){var n=this.options;if(void 0===e)return n[t];var o=L.modifyOption(this,t,e);n[t]=void 0!==o?o:e,"group"===t&&It(n)},destroy:function(){W("destroy",this);var t=this.el;t[F]=null,m(t,"mousedown",this._onTapStart),m(t,"touchstart",this._onTapStart),m(t,"pointerdown",this._onTapStart),this.nativeDraggable&&(m(t,"dragover",this),m(t,"dragenter",this)),Array.prototype.forEach.call(t.querySelectorAll("[draggable]"),(function(t){t.removeAttribute("draggable")})),this._onDrop(),this._disableDelayedDragEvents(),wt.splice(wt.indexOf(this.el),1),this.el=t=null},_hideClone:function(){tt||(W("hideClone",this),Yt.eventCanceled||(D(J,"display","none"),this.options.removeCloneOnHide&&J.parentNode&&J.parentNode.removeChild(J),tt=!0))},_showClone:function(t){"clone"===t.lastPutMode?tt&&(W("showClone",this),Yt.eventCanceled||(U.parentNode!=Z||this.options.group.revertClone?$?Z.insertBefore(J,$):Z.appendChild(J):Z.insertBefore(J,U),this.options.group.revertClone&&this.animate(U,J),D(J,"display",""),tt=!1)):this._hideClone()}},Tt&&g(document,"touchmove",(function(t){(Yt.active||bt)&&t.cancelable&&t.preventDefault()})),Yt.utils={on:g,off:m,css:D,find:_,is:function(t,e){return!!b(t,e,t,!1)},extend:function(t,e){if(t&&e)for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t},throttle:k,closest:b,toggleClass:E,clone:X,index:M,nextTick:Ht,cancelNextTick:Lt,detectDirection:Nt,getChild:O},Yt.get=function(t){return t[F]},Yt.mount=function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];(n=n[0].constructor===Array?n[0]:n).forEach((function(t){if(!t.prototype||!t.prototype.constructor)throw"Sortable: Mounted plugin must be a constructor function, not ".concat({}.toString.call(t));t.utils&&(Yt.utils=e(e({},Yt.utils),t.utils)),L.mount(t)}))},Yt.create=function(t,e){return new Yt(t,e)};var Kt,Wt,zt,Gt,Ut,qt,Vt=[],Zt=!(Yt.version="1.15.0");function $t(){Vt.forEach((function(t){clearInterval(t.pid)})),Vt=[]}function Qt(){clearInterval(qt)}var Jt,te=k((function(t,e,n,o){if(e.scroll){var i,r=(t.touches?t.touches[0]:t).clientX,a=(t.touches?t.touches[0]:t).clientY,l=e.scrollSensitivity,s=e.scrollSpeed,c=C(),u=!1;Wt!==n&&(Wt=n,$t(),Kt=e.scroll,i=e.scrollFn,!0===Kt&&(Kt=I(n,!0)));var d=0,h=Kt;do{var f=h,p=(O=T(f)).top,g=O.bottom,m=O.left,v=O.right,b=O.width,y=O.height,w=void 0,E=f.scrollWidth,S=f.scrollHeight,_=D(f),x=f.scrollLeft,O=f.scrollTop,A=f===c?(w=b<E&&("auto"===_.overflowX||"scroll"===_.overflowX||"visible"===_.overflowX),y<S&&("auto"===_.overflowY||"scroll"===_.overflowY||"visible"===_.overflowY)):(w=b<E&&("auto"===_.overflowX||"scroll"===_.overflowX),y<S&&("auto"===_.overflowY||"scroll"===_.overflowY));x=w&&(Math.abs(v-r)<=l&&x+b<E)-(Math.abs(m-r)<=l&&!!x),O=A&&(Math.abs(g-a)<=l&&O+y<S)-(Math.abs(p-a)<=l&&!!O);if(!Vt[d])for(var M=0;M<=d;M++)Vt[M]||(Vt[M]={});Vt[d].vx==x&&Vt[d].vy==O&&Vt[d].el===f||(Vt[d].el=f,Vt[d].vx=x,Vt[d].vy=O,clearInterval(Vt[d].pid),0==x&&0==O||(u=!0,Vt[d].pid=setInterval(function(){o&&0===this.layer&&Yt.active._onTouchMove(Ut);var e=Vt[this.layer].vy?Vt[this.layer].vy*s:0,n=Vt[this.layer].vx?Vt[this.layer].vx*s:0;"function"==typeof i&&"continue"!==i.call(Yt.dragged.parentNode[F],n,e,t,Ut,Vt[this.layer].el)||R(Vt[this.layer].el,n,e)}.bind({layer:d}),24))),d++}while(e.bubbleScroll&&h!==c&&(h=I(h,!1)));Zt=u}}),30);f=function(t){var e=t.originalEvent,n=t.putSortable,o=t.dragEl,i=t.activeSortable,r=t.dispatchSortableEvent,a=t.hideGhostForTarget;t=t.unhideGhostForTarget;e&&(i=n||i,a(),e=e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e,e=document.elementFromPoint(e.clientX,e.clientY),t(),i&&!i.el.contains(e)&&(r("spill"),this.onSpill({dragEl:o,putSortable:n})))};function ee(){}function ne(){}ee.prototype={startIndex:null,dragStart:function(t){t=t.oldDraggableIndex,this.startIndex=t},onSpill:function(t){var e=t.dragEl,n=t.putSortable;this.sortable.captureAnimationState(),n&&n.captureAnimationState(),(t=O(this.sortable.el,this.startIndex,this.options))?this.sortable.el.insertBefore(e,t):this.sortable.el.appendChild(e),this.sortable.animateAll(),n&&n.animateAll()},drop:f},o(ee,{pluginName:"revertOnSpill"}),ne.prototype={onSpill:function(t){var e=t.dragEl;(t=t.putSortable||this.sortable).captureAnimationState(),e.parentNode&&e.parentNode.removeChild(e),t.animateAll()},drop:f},o(ne,{pluginName:"removeOnSpill"});var oe,ie,re,ae,le,se=[],ce=[],ue=!1,de=!1,he=!1;function fe(t,e){ce.forEach((function(n,o){(o=e.children[n.sortableIndex+(t?Number(o):0)])?e.insertBefore(n,o):e.appendChild(n)}))}function pe(){se.forEach((function(t){t!==re&&t.parentNode&&t.parentNode.removeChild(t)}))}return Yt.mount(new function(){function t(){for(var t in this.defaults={scroll:!0,forceAutoScrollFallback:!1,scrollSensitivity:30,scrollSpeed:10,bubbleScroll:!0},this)"_"===t.charAt(0)&&"function"==typeof this[t]&&(this[t]=this[t].bind(this))}return t.prototype={dragStarted:function(t){t=t.originalEvent,this.sortable.nativeDraggable?g(document,"dragover",this._handleAutoScroll):this.options.supportPointer?g(document,"pointermove",this._handleFallbackAutoScroll):t.touches?g(document,"touchmove",this._handleFallbackAutoScroll):g(document,"mousemove",this._handleFallbackAutoScroll)},dragOverCompleted:function(t){t=t.originalEvent,this.options.dragOverBubble||t.rootEl||this._handleAutoScroll(t)},drop:function(){this.sortable.nativeDraggable?m(document,"dragover",this._handleAutoScroll):(m(document,"pointermove",this._handleFallbackAutoScroll),m(document,"touchmove",this._handleFallbackAutoScroll),m(document,"mousemove",this._handleFallbackAutoScroll)),Qt(),$t(),clearTimeout(y),y=void 0},nulling:function(){Ut=Wt=Kt=Zt=qt=zt=Gt=null,Vt.length=0},_handleFallbackAutoScroll:function(t){this._handleAutoScroll(t,!0)},_handleAutoScroll:function(t,e){var n,o=this,i=(t.touches?t.touches[0]:t).clientX,r=(t.touches?t.touches[0]:t).clientY,a=document.elementFromPoint(i,r);Ut=t,e||this.options.forceAutoScrollFallback||c||s||d?(te(t,this.options,a,e),n=I(a,!0),!Zt||qt&&i===zt&&r===Gt||(qt&&Qt(),qt=setInterval((function(){var a=I(document.elementFromPoint(i,r),!0);a!==n&&(n=a,$t()),te(t,o.options,a,e)}),10),zt=i,Gt=r)):this.options.bubbleScroll&&I(a,!0)!==C()?te(t,this.options,I(a,!1),!1):$t()}},o(t,{pluginName:"scroll",initializeByDefault:!0})}),Yt.mount(ne,ee),Yt.mount(new function(){function t(){this.defaults={swapClass:"sortable-swap-highlight"}}return t.prototype={dragStart:function(t){t=t.dragEl,Jt=t},dragOverValid:function(t){var e=t.completed,n=t.target,o=t.onMove,i=t.activeSortable,r=t.changed,a=t.cancel;i.options.swap&&(t=this.sortable.el,i=this.options,n&&n!==t&&(t=Jt,Jt=!1!==o(n)?(E(n,i.swapClass,!0),n):null,t&&t!==Jt&&E(t,i.swapClass,!1)),r(),e(!0),a())},drop:function(t){var e,n,o=t.activeSortable,i=t.putSortable,r=t.dragEl,a=i||this.sortable,l=this.options;Jt&&E(Jt,l.swapClass,!1),Jt&&(l.swap||i&&i.options.swap)&&r!==Jt&&(a.captureAnimationState(),a!==o&&o.captureAnimationState(),n=Jt,t=(e=r).parentNode,l=n.parentNode,t&&l&&!t.isEqualNode(n)&&!l.isEqualNode(e)&&(i=M(e),r=M(n),t.isEqualNode(l)&&i<r&&r++,t.insertBefore(n,t.children[i]),l.insertBefore(e,l.children[r])),a.animateAll(),a!==o&&o.animateAll())},nulling:function(){Jt=null}},o(t,{pluginName:"swap",eventProperties:function(){return{swapItem:Jt}}})}),Yt.mount(new function(){function t(t){for(var e in this)"_"===e.charAt(0)&&"function"==typeof this[e]&&(this[e]=this[e].bind(this));t.options.avoidImplicitDeselect||(t.options.supportPointer?g(document,"pointerup",this._deselectMultiDrag):(g(document,"mouseup",this._deselectMultiDrag),g(document,"touchend",this._deselectMultiDrag))),g(document,"keydown",this._checkKeyDown),g(document,"keyup",this._checkKeyUp),this.defaults={selectedClass:"sortable-selected",multiDragKey:null,avoidImplicitDeselect:!1,setData:function(e,n){var o="";se.length&&ie===t?se.forEach((function(t,e){o+=(e?", ":"")+t.textContent})):o=n.textContent,e.setData("Text",o)}}}return t.prototype={multiDragKeyDown:!1,isMultiDrag:!1,delayStartGlobal:function(t){t=t.dragEl,re=t},delayEnded:function(){this.isMultiDrag=~se.indexOf(re)},setupClone:function(t){var e=t.sortable;t=t.cancel;if(this.isMultiDrag){for(var n=0;n<se.length;n++)ce.push(X(se[n])),ce[n].sortableIndex=se[n].sortableIndex,ce[n].draggable=!1,ce[n].style["will-change"]="",E(ce[n],this.options.selectedClass,!1),se[n]===re&&E(ce[n],this.options.chosenClass,!1);e._hideClone(),t()}},clone:function(t){var e=t.sortable,n=t.rootEl,o=t.dispatchSortableEvent;t=t.cancel;this.isMultiDrag&&(this.options.removeCloneOnHide||se.length&&ie===e&&(fe(!0,n),o("clone"),t()))},showClone:function(t){var e=t.cloneNowShown,n=t.rootEl;t=t.cancel;this.isMultiDrag&&(fe(!1,n),ce.forEach((function(t){D(t,"display","")})),e(),le=!1,t())},hideClone:function(t){var e=this,n=(t.sortable,t.cloneNowHidden);t=t.cancel;this.isMultiDrag&&(ce.forEach((function(t){D(t,"display","none"),e.options.removeCloneOnHide&&t.parentNode&&t.parentNode.removeChild(t)})),n(),le=!0,t())},dragStartGlobal:function(t){t.sortable,!this.isMultiDrag&&ie&&ie.multiDrag._deselectMultiDrag(),se.forEach((function(t){t.sortableIndex=M(t)})),se=se.sort((function(t,e){return t.sortableIndex-e.sortableIndex})),he=!0},dragStarted:function(t){var e,n=this;t=t.sortable;this.isMultiDrag&&(this.options.sort&&(t.captureAnimationState(),this.options.animation&&(se.forEach((function(t){t!==re&&D(t,"position","absolute")})),e=T(re,!1,!0,!0),se.forEach((function(t){t!==re&&Y(t,e)})),ue=de=!0)),t.animateAll((function(){ue=de=!1,n.options.animation&&se.forEach((function(t){B(t)})),n.options.sort&&pe()})))},dragOver:function(t){var e=t.target,n=t.completed;t=t.cancel;de&&~se.indexOf(e)&&(n(!1),t())},revert:function(t){var e,n,o=t.fromSortable,i=t.rootEl,r=t.sortable,a=t.dragRect;1<se.length&&(se.forEach((function(t){r.addAnimationState({target:t,rect:de?T(t):a}),B(t),t.fromRect=a,o.removeAnimationState(t)})),de=!1,e=!this.options.removeCloneOnHide,n=i,se.forEach((function(t,o){(o=n.children[t.sortableIndex+(e?Number(o):0)])?n.insertBefore(t,o):n.appendChild(t)})))},dragOverCompleted:function(t){var e,n=t.sortable,o=t.isOwner,i=t.insertion,r=t.activeSortable,a=t.parentEl,l=t.putSortable;t=this.options;i&&(o&&r._hideClone(),ue=!1,t.animation&&1<se.length&&(de||!o&&!r.options.sort&&!l)&&(e=T(re,!1,!0,!0),se.forEach((function(t){t!==re&&(Y(t,e),a.appendChild(t))})),de=!0),o||(de||pe(),1<se.length?(o=le,r._showClone(n),r.options.animation&&!le&&o&&ce.forEach((function(t){r.addAnimationState({target:t,rect:ae}),t.fromRect=ae,t.thisAnimationDuration=null}))):r._showClone(n)))},dragOverAnimationCapture:function(t){var e=t.dragRect,n=t.isOwner;t=t.activeSortable;se.forEach((function(t){t.thisAnimationDuration=null})),t.options.animation&&!n&&t.multiDrag.isMultiDrag&&(ae=o({},e),e=S(re,!0),ae.top-=e.f,ae.left-=e.e)},dragOverAnimationComplete:function(){de&&(de=!1,pe())},drop:function(t){var e=t.originalEvent,n=t.rootEl,o=t.parentEl,i=t.sortable,r=t.dispatchSortableEvent,a=t.oldIndex,l=t.putSortable,s=l||this.sortable;if(e){var c,u,d,h=this.options,f=o.children;if(!he)if(h.multiDragKey&&!this.multiDragKeyDown&&this._deselectMultiDrag(),E(re,h.selectedClass,!~se.indexOf(re)),~se.indexOf(re))se.splice(se.indexOf(re),1),oe=null,K({sortable:i,rootEl:n,name:"deselect",targetEl:re,originalEvent:e});else{if(se.push(re),K({sortable:i,rootEl:n,name:"select",targetEl:re,originalEvent:e}),e.shiftKey&&oe&&i.el.contains(oe)){var p=M(oe);t=M(re);if(~p&&~t&&p!==t)for(var g,m=p<t?(g=p,t):(g=t,p+1);g<m;g++)~se.indexOf(f[g])||(E(f[g],h.selectedClass,!0),se.push(f[g]),K({sortable:i,rootEl:n,name:"select",targetEl:f[g],originalEvent:e}))}else oe=re;ie=s}he&&this.isMultiDrag&&(de=!1,(o[F].options.sort||o!==n)&&1<se.length&&(c=T(re),u=M(re,":not(."+this.options.selectedClass+")"),!ue&&h.animation&&(re.thisAnimationDuration=null),s.captureAnimationState(),ue||(h.animation&&(re.fromRect=c,se.forEach((function(t){var e;t.thisAnimationDuration=null,t!==re&&(e=de?T(t):c,t.fromRect=e,s.addAnimationState({target:t,rect:e}))}))),pe(),se.forEach((function(t){f[u]?o.insertBefore(t,f[u]):o.appendChild(t),u++})),a===M(re)&&(d=!1,se.forEach((function(t){t.sortableIndex!==M(t)&&(d=!0)})),d&&r("update"))),se.forEach((function(t){B(t)})),s.animateAll()),ie=s),(n===o||l&&"clone"!==l.lastPutMode)&&ce.forEach((function(t){t.parentNode&&t.parentNode.removeChild(t)}))}},nullingGlobal:function(){this.isMultiDrag=he=!1,ce.length=0},destroyGlobal:function(){this._deselectMultiDrag(),m(document,"pointerup",this._deselectMultiDrag),m(document,"mouseup",this._deselectMultiDrag),m(document,"touchend",this._deselectMultiDrag),m(document,"keydown",this._checkKeyDown),m(document,"keyup",this._checkKeyUp)},_deselectMultiDrag:function(t){if(!(void 0!==he&&he||ie!==this.sortable||t&&b(t.target,this.options.draggable,this.sortable.el,!1)||t&&0!==t.button))for(;se.length;){var e=se[0];E(e,this.options.selectedClass,!1),se.shift(),K({sortable:this.sortable,rootEl:this.sortable.el,name:"deselect",targetEl:e,originalEvent:t})}},_checkKeyDown:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!0)},_checkKeyUp:function(t){t.key===this.options.multiDragKey&&(this.multiDragKeyDown=!1)}},o(t,{pluginName:"multiDrag",utils:{select:function(t){var e=t.parentNode[F];e&&e.options.multiDrag&&!~se.indexOf(t)&&(ie&&ie!==e&&(ie.multiDrag._deselectMultiDrag(),ie=e),E(t,e.options.selectedClass,!0),se.push(t))},deselect:function(t){var e=t.parentNode[F],n=se.indexOf(t);e&&e.options.multiDrag&&~n&&(E(t,e.options.selectedClass,!1),se.splice(n,1))}},eventProperties:function(){var t=this,e=[],n=[];return se.forEach((function(o){var i;e.push({multiDragElement:o,index:o.sortableIndex}),i=de&&o!==re?-1:de?M(o,":not(."+t.options.selectedClass+")"):M(o),n.push({multiDragElement:o,index:i})})),{items:r(se),clones:[].concat(ce),oldIndicies:e,newIndicies:n}},optionListeners:{multiDragKey:function(t){return"ctrl"===(t=t.toLowerCase())?t="Control":1<t.length&&(t=t.charAt(0).toUpperCase()+t.substr(1)),t}}})}),Yt}));
