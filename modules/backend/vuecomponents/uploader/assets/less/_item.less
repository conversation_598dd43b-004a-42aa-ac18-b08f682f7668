.uploader-body {
    ul {
        padding: 0;
        margin: 0;

        li {
            padding: 15px 20px;
            max-width: 330px;
            margin: 0;
            box-sizing: border-box;
            list-style: none;
            border-bottom: 1px solid @primary-border;
            position: relative;
            white-space: nowrap;

            &:last-child {
                border-bottom: none;
            }

            .item-name {
                text-overflow: ellipsis;
                overflow: hidden;
                display: block;
            }

            .item-error {
                white-space: normal;
                display: inline-block;
                margin-top: 6px;
            }

            &.status-error {
                .item-name {
                    color: @brand-danger;
                }
            }

            &.status-completed {
                padding-right: 50px;

                i.backend-icon-background {
                    position: absolute;
                    right: 20px;
                    top: 17px;
                    background-position: -223px -40px;
                    width: 16px;
                    height: 16px;
                }
            }

            &.status-uploading {
                padding-right: 123px;

                .component-backend-loading-indicator {
                    position: absolute;
                    width: 65px;
                    top: 22px;
                    right: 48px;
                }

                .close {
                    top: 16px;
                }
            }
        }
    }
}
