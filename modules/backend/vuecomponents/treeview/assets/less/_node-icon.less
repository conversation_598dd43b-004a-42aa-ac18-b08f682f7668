ul li {
    span.node-icon {
        display: inline-block;
        position: absolute;
        top: 7px;
        border-radius: 4px;
        width: @treeview-icon-size;
        height: @treeview-icon-size;
        margin-right: @treeview-icon-margin;
        vertical-align: text-bottom;
        box-shadow: 0 0 0 1px rgba(var(--bs-body-bg-rgb), 0.3);

        &.folder-icon {
            box-shadow: none;
        }

        i {
            color: white;
            position: absolute;
            left: 3px;
            top: 3px;
            font-size: 10px;

            &.treeview-folder {
                width: 16px;
                height: 16px;
                background-position: 0 -20px;
                left: 0;
                top: 0;
            }
        }
    }

    &.selected-node {
        > .item-label-outer-container {
            > .item-label-container {
                span.node-icon .treeview-folder {
                    background-position: 0 -73px;
                }
            }
        }
    }

    &.mode-tree {
        span.node-icon {
            left: @treeview-toggle-control-width + @treeview-toggle-control-margin;
        }
    }
}
