td {
    @dropdown-height: 36px;

    .inspector-table-dropdown-container {
        padding-right: 0;

        .dropdown-placeholder {
            position: absolute;
            padding: 0 42px 0 12px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            width: 100%;
            left: 0;
            top: 0;
            line-height: @dropdown-height;
            color: @inspector-placeholder-color;
        }
    }

    div.multiselect {
        .multiselect__select {
            height: @dropdown-height;
            width: 20px;

            &:before {
                top: 8px;
            }
        }

        .multiselect__tags {
            padding-right: 20px;
            height: @dropdown-height;
            line-height: @dropdown-height;
            min-height: @dropdown-height;
        }

        .multiselect__single {
            line-height: @dropdown-height;
            height: @dropdown-height;
        }

        .multiselect__input {
            line-height: @dropdown-height;
            height: @dropdown-height;
        }
    }
}