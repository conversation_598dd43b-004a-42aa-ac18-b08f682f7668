@import "../../../../assets/less/core/boot.less";

.component-backend-richeditor-document-connector {
    div.richeditor-container {
        display: block;
        position: relative;
    }

    div.codeeditor-container {
        display: none;
    }

    &.code-editing-mode {
        div.richeditor-container {
            display: none;
        }

        div.codeeditor-container {
            display: block;
        }
    }

    .component-backend-richeditor {
        position: absolute;
        width: 100%;
        height: 100%;

        .fr-box {
            position: absolute !important;
        }

        .fr-toolbar {
            // display: none;
            height: 0;
            padding: 0;
            border: none;

            > button,
            > div.fr-dropdown-menu,
            > div.fr-separator,
            > div.fr-btn-wrap {
                display: none;
            }
        }

        .fr-popup {
            top: 0 !important;
        }
    }

    iframe.fr-iframe {
        min-height: 100%;
    }

    @import "_resizing.less";
}

body .fr-popup {
    border: @popup-border !important;
    border-radius: 8px;
    box-shadow: @box-shadow-z2 !important;
    background-color: @popup-bg;

    .fr-buttons {
        border-bottom: none !important;

        &:empty {
            border-bottom: none;
        }
    }

    .fr-arrow {
        display: none;
    }

    .fr-action-buttons {
        padding: 5px 0;
        height: auto;
    }
}

body.richeditor-document-connector-resizing {
    * {
        user-select: none;
        cursor: col-resize !important;
    }
}

@import "_formwidget.less";