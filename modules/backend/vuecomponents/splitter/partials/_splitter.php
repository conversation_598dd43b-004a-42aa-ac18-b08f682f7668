<div
    class="component-backend-splitter" :class="cssClass"
>
    <div class="flex-layout-item fix splitter-first-panel" :style="firstPanelStyle" ref="firstPanel">
        <slot name="first">
            Left panel
        </slot>
    </div>

    <div
        class="flex-layout-item fix splitter-handle"
        :class="{dragging: dragging}"
        ref="handle"
        @mousedown.stop="onHandleMouseDown"
    ></div>

    <div class="flex-layout-item stretch relative">
        <slot name="second">
            Right panel
        </slot>
    </div>
</div>