# ===================================
#  Field Definitions
# ===================================

tabs:
    defaultTab: backend::lang.user.group.name

    fields:
        is_new_user_default:
            label: backend::lang.user.group.is_new_user_default_field_label
            comment: backend::lang.user.group.is_new_user_default_field_comment
            type: switch

        name:
            label: backend::lang.user.group.name_field
            comment: backend::lang.user.group.name_comment
            span: auto

        code:
            label: backend::lang.user.group.code_field
            comment: backend::lang.user.group.code_comment
            span: auto

        description:
            label: backend::lang.user.group.description_field
            type: textarea
            size: tiny

        users:
            type: partial
            path: relation_users
            tab: backend::lang.user.group.users_count
