<?php if (empty($hintName) || !$this->isBackendHintHidden($hintName)): ?>
    <?php
        $type = !empty($type) ? $type : 'info';
        $cssClasses = ['callout fade show'];
        $cssClasses[] = 'callout-'.$type;
        if (empty($icon)) $cssClasses[] = 'no-icon';
        if (empty($subtitle)) $cssClasses[] = 'no-subheader';
        if (empty($title)) $cssClasses[] = 'no-title';
        if (!empty($cssClass)) $cssClasses[] = $cssClass;
    ?>
    <div class="<?= implode(' ', $cssClasses) ?>">
        <?php if (!empty($hintName)): ?>
            <button
                type="button"
                class="close"
                data-request="onHideBackendHint"
                data-request-data="name: '<?= $hintName ?>'"
                data-dismiss="callout"
                aria-hidden="true">&times;</button>
        <?php endif ?>
        <?php if (!empty($title)): ?>
            <div class="header">
                <?php if (!empty($icon)): ?><i class="<?= $icon ?>"></i><?php endif ?>
                <h3><?= $title ?></h3>
                <?php if (!empty($subtitle)): ?><p><?= $subtitle ?></p><?php endif ?>
            </div>
        <?php endif ?>
        <div class="content">
            <?php if ($hintContent): ?>
                <?= $hintContent ?>
            <?php else: ?>
                <?= $this->makePartial($hintPartial, $hintParams) ?>
            <?php endif ?>
        </div>
    </div>
<?php endif ?>
