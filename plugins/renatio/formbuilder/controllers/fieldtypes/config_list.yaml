# ===================================
#  List Behavior Config
# ===================================

# Model List Column configuration
list: $/renatio/formbuilder/models/fieldtype/columns.yaml

# Model Class name
modelClass: Renatio\FormBuilder\Models\FieldType

# List Title
title: renatio.formbuilder::lang.field_type.manage

# Link URL for each record
recordUrl: renatio/formbuilder/fieldtypes/update/:id

# Message to display if the list is empty
noRecordsMessage: backend::lang.list.no_records

# Records to display per page
recordsPerPage: 20

# Displays the list column set up button
showSetup: true

# Displays the sorting link on each column
showSorting: true

# Default sorting column
defaultSort:
    column: name
    direction: asc

# Display checkboxes next to each record
showCheckboxes: true

# Toolbar widget configuration
toolbar:
    # Partial for toolbar buttons
    buttons: list_toolbar

    # Search widget configuration
    search:
        prompt: backend::lang.list.search_prompt
