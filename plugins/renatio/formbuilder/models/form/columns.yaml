# ===================================
#  List Column Definitions
# ===================================

columns:

    name:
        label: renatio.formbuilder::lang.field.name
        searchable: true

    code:
        label: renatio.formbuilder::lang.field.code
        searchable: true

    template_code:
        label: renatio.formbuilder::lang.field.template
        searchable: true

    created_at:
        label: renatio.formbuilder::lang.field.created_at
        type: datetime

    updated_at:
        label: renatio.formbuilder::lang.field.updated_at
        type: datetime

    actions:
        label: renatio.formbuilder::lang.field.actions
        type: partial
        path: column_actions
        clickable: false
        sortable: false
        align: center
        cssClass: 'column-button'
        width: '110px'
