<?php namespace Invato\Airconditioners\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInvatoAirconditionersAircos extends Migration
{
    public function up()
    {
        Schema::create('invato_airconditioners_aircos', function($table)
        {
            $table->increments('id')->unsigned();
            $table->string('title')->nullable();
            $table->string('slug')->nullable();
            $table->text('description');
            $table->decimal('capacity', 3, 1)->default(0.0);
            $table->decimal('price', 8, 2)->default(0.00);
            $table->decimal('price_new', 8, 2)->nullable();
            $table->text('images')->nullable();
            $table->string('energylabel_cooling')->nullable();
            $table->string('energylabel_heating')->nullable();
            $table->text('custom_options')->nullable();
            $table->text('specifications');
            $table->boolean('is_active')->default(1);
            $table->integer('sort_order')->default(0);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('invato_airconditioners_aircos');
    }
}
