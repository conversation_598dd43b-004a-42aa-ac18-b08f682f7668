<?php namespace Invato\Agenda\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoAgendaEvents3 extends Migration
{
    public function up()
    {
        Schema::table('invato_agenda_events', function($table)
        {
            $table->date('date_end')->nullable()->after('date');
        });
    }
    
    public function down()
    {
        Schema::table('invato_agenda_events', function($table)
        {
            $table->dropColumn('date_end');
        });
    }
}
