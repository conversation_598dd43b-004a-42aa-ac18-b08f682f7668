<?php namespace Invato\Testimonials\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInvatoTestimonialsReviews extends Migration
{
    public function up()
    {
        Schema::create('invato_testimonials_reviews', function($table)
        {
            $table->increments('id')->unsigned();
            $table->string('name');
            $table->date('date')->nullable();
            $table->smallInteger('score')->nullable()->default(0);
            $table->text('review')->nullable();
            $table->boolean('is_approved')->default(0);
            $table->integer('sort_order')->default(0);
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('invato_testimonials_reviews');
    }
}
