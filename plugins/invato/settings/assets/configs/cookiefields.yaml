fields: { }
tabs:
    fields:

        ## Cookies

        cookies:
            prompt: 'janvince.smallgdpr::lang.settings.form_fields.cookies_list_prompt'
            span: full
            type: repeater
            style: collapsed
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_list'
            form:
                fields:
                    title:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.title'
                        type: text
                        span: left
                        required: true
                    description:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.description'
                        type: richeditor
                        span: right
                        size: large
                    slug:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.slug'
                        type: text
                        span: left
                        preset:
                            field: title
                            type: slug
                        required: true
                    required:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.cookie_required'
                        comment: 'janvince.smallgdpr::lang.settings.form_fields.cookie_required_description'
                        type: checkbox
                        span: left
                        default: false
                    default_enabled:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.cookie_default_enabled'
                        comment: 'janvince.smallgdpr::lang.settings.form_fields.cookie_default_enabled_description'
                        type: checkbox
                        span: left
                        default: false
                    default_modal_checked:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.cookie_default_modal_checked'
                        comment: 'janvince.smallgdpr::lang.settings.form_fields.cookie_default_modal_checked_description'
                        type: checkbox
                        span: left
                        default: false
                        trigger:
                            action: hide
                            field: required
                            condition: checked
                    # Custom Added By Kevin
                    # cookie_list_section:
                    #     label: Cookie Lijst
                    #     type: section
                    # cookie_list:
                    #     type: repeater
                    #     prompt: Aanbieder Toevoegen
                    #     itemsExpanded: false
                    #     form:
                    #         fields:
                    #             title:
                    #                 label: Titel
                    #             provider_url:
                    #                 label: URL van aanbieder
                    #             cookies:
                    #                 label: Cookies
                    #                 type: repeater
                    #                 itemsExpanded: false
                    #                 prompt: Cookie Toevoegen
                    #                 form:
                    #                     fields:
                    #                         title:
                    #                             label: Titel
                    #                         lifetime:
                    #                             label: Vervaltermijn
                    #                         type:
                    #                             label: Type
                    #                             type: dropdown
                    #                             options:
                    #                                 'HTTP': 'HTTP'
                    #                                 'Pixel': 'Pixel'
                    #                         description:
                    #                             label: Omschrijving
                    #                             type: textarea
                    #                             size: small
                    # End Custom

                    scripts_code_section:
                        comment: 'janvince.smallgdpr::lang.settings.form_fields.scripts'
                        type: section
                    scripts:
                        prompt: 'janvince.smallgdpr::lang.settings.form_fields.scripts_prompt'
                        span: full
                        type: repeater
                        style: collapsed
                        titleFrom: scripts_title
                        tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_list'
                        form:
                            fields:
                                scripts_title:
                                    label: 'janvince.smallgdpr::lang.settings.form_fields.scripts_title'
                                    comment: 'janvince.smallgdpr::lang.settings.form_fields.scripts_title_description'
                                    type: text
                                    span: left
                                scripts_file:
                                    label: 'janvince.smallgdpr::lang.settings.form_fields.scripts_file'
                                    comment: 'janvince.smallgdpr::lang.settings.form_fields.scripts_file_description'
                                    type: mediafinder
                                    mode: file
                                    span: right
                                scripts_run_production:
                                    label: 'janvince.smallgdpr::lang.settings.form_fields.scripts_run_production'
                                    comment: 'janvince.smallgdpr::lang.settings.form_fields.scripts_run_production_description'
                                    type: checkbox
                                    span: left
                                scripts_code:
                                    label: 'janvince.smallgdpr::lang.settings.form_fields.scripts_code'
                                    comment: 'janvince.smallgdpr::lang.settings.form_fields.scripts_code_description'
                                    type: codeeditor
                                    size: huge
                                    span: right
                                    language: javascript
                                    fontSize: 12px
                                scripts_disable:
                                    label: 'janvince.smallgdpr::lang.settings.form_fields.scripts_disable'
                                    comment: 'janvince.smallgdpr::lang.settings.form_fields.scripts_disable_description'
                                    type: checkbox
                                    default: false
                                    span: left
                                scripts_run_pages:
                                    label: 'janvince.smallgdpr::lang.settings.form_fields.scripts_run_pages'
                                    comment: 'janvince.smallgdpr::lang.settings.form_fields.scripts_run_pages_description'
                                    type: checkbox
                                    default: false
                                    span: left
                                scripts_run_pages_list:
                                    label: 'janvince.smallgdpr::lang.settings.form_fields.scripts_run_pages_list'
                                    prompt: 'janvince.smallgdpr::lang.settings.form_fields.scripts_run_pages_list_prompt'
                                    span: left
                                    type: repeater
                                    style: collapsed
                                    titleFrom: page_url
                                    cssClass: field-indent
                                    tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_list'
                                    trigger:
                                        action: show
                                        field: scripts_run_pages
                                        condition: checked
                                    form:
                                        fields:
                                            page_url:
                                                label: 'janvince.smallgdpr::lang.settings.form_fields.page_url'
                                                comment: 'janvince.smallgdpr::lang.settings.form_fields.page_url_description'
                                                type: text
                                                span: full


        ## Cookies bar

        cookies_bar_title:
            span: left
            label: 'janvince.smallgdpr::lang.settings.form_fields.title'
            type: text
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_bar'
        cookies_bar_content:
            span: right
            label: 'janvince.smallgdpr::lang.settings.form_fields.content'
            type: richeditor
            size: large
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_bar'
        cookies_bar_css_class:
            label: 'janvince.smallgdpr::lang.settings.form_fields.css_class'
            type: text
            span: left
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_bar'
        cookies_bar_add_styles:
            label: 'janvince.smallgdpr::lang.settings.form_fields.add_css'
            comment: 'janvince.smallgdpr::lang.settings.form_fields.add_css_description'
            type: dropdown
            span: left
            emptyOption: 'janvince.smallgdpr::lang.settings.form_fields.add_css_empty_option'
            cssClass: m-t
            options:
                1: 'janvince.smallgdpr::lang.settings.form_fields.add_css_option_rbbox'
                2: 'janvince.smallgdpr::lang.settings.form_fields.add_css_option_topline'
                3: 'janvince.smallgdpr::lang.settings.form_fields.add_css_option_topline_container'
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_bar'
        cookies_bar_disable_page_reload:
            label: 'janvince.smallgdpr::lang.settings.form_fields.disable_page_reload'
            comment: 'janvince.smallgdpr::lang.settings.form_fields.disable_page_reload_description'
            type: checkbox
            span: left
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_bar'
        cookies_bar_buttons_section:
            label: 'janvince.smallgdpr::lang.settings.form_fields.cookies_bar_buttons'
            type: section
            span: full
            cssClass: m-t
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_bar'
        cookies_bar_buttons:
            prompt: 'janvince.smallgdpr::lang.settings.form_fields.cookies_bar_buttons_prompt'
            span: full
            type: repeater
            style: collapsed
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_bar'
            form:
                fields:
                    title:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.title'
                        type: text
                        span: left
                        required: true
                    url:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.url'
                        comment: 'janvince.smallgdpr::lang.settings.form_fields.url_description'
                        type: text
                        span: right
                        # trigger:
                        #     action: hide
                        #     field: accept_all_cookies_btn
                        #     action: show
                        #     condition: checked
                    accept_all_cookies_btn:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.accept_all_cookies_btn'
                        comment: 'janvince.smallgdpr::lang.settings.form_fields.accept_all_cookies_btn_description'
                        type: checkbox
                        span: left
                    url_external:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.url_external'
                        comment: 'janvince.smallgdpr::lang.settings.form_fields.url_external_description'
                        type: checkbox
                        span: right
                        # trigger:
                        #     action: hide
                        #     field: accept_all_cookies_btn
                        #     action: show
                        #     condition: checked
                    disable_all_cookies_btn:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.disable_all_cookies_btn'
                        comment: 'janvince.smallgdpr::lang.settings.form_fields.disable_all_cookies_btn_description'
                        type: checkbox
                        span: left
                    accept_all_cookies_btn_script:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.accept_all_cookies_btn_script'
                        comment: 'janvince.smallgdpr::lang.settings.form_fields.accept_all_cookies_btn_script_description'
                        type: codeeditor
                        span: full
                        trigger:
                            field: accept_all_cookies_btn
                            action: show
                            condition: checked
                    section_01:
                        type: section
                    css_class:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.css_class'
                        type: text
                        span: left
                    html_attributes:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.html_attributes'
                        type: text
                        span: right
                    section_02:
                        type: section
                    show_modal:
                        label: 'janvince.smallgdpr::lang.settings.form_fields.show_modal'
                        comment: 'janvince.smallgdpr::lang.settings.form_fields.show_modal_description'
                        type: checkbox
                        span: left


        ## Cookies manage

        cookies_manage_title:
            label: 'janvince.smallgdpr::lang.settings.form_fields.title'
            type: text
            span: left
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_manage'
        cookies_manage_css_class:
            label: 'janvince.smallgdpr::lang.settings.form_fields.css_class'
            type: text
            span: right
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_manage'
        cookies_manage_content:
            label: 'janvince.smallgdpr::lang.settings.form_fields.content'
            type: richeditor
            span: full
            size: large
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_manage'
        cookies_about_content:
            label: Over cookies
            type: richeditor
            span: full
            size: large
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_manage'
        cookies_manage_disable_page_reload:
            label: 'janvince.smallgdpr::lang.settings.form_fields.disable_page_reload'
            comment: 'janvince.smallgdpr::lang.settings.form_fields.disable_page_reload_description'
            type: checkbox
            span: left
            tab: 'janvince.smallgdpr::lang.settings.tabs.cookies_manage'


        ## Settings

        set_cookies_lifetime_days:
            label: 'janvince.smallgdpr::lang.settings.form_fields.cookies_lifetime_days'
            comment: 'janvince.smallgdpr::lang.settings.form_fields.cookies_lifetime_days_comment'
            default: 365
            step: 1
            min: 1
            type: number
            span: left
            tab: 'janvince.smallgdpr::lang.settings.tabs.settings'
        ui_style:
            label: 'janvince.smallgdpr::lang.settings.form_fields.ui_style'
            comment: 'janvince.smallgdpr::lang.settings.form_fields.ui_style_description'
            type: dropdown
            span: left
            tab: 'janvince.smallgdpr::lang.settings.tabs.settings'
            emptyOption: 'janvince.smallgdpr::lang.settings.form_fields.ui_style_option_empty'
            options:
                'b3': 'Bootstrap 3'
                'b5': 'Bootstrap 5'
        set_cookies_with_locale:
            label: 'janvince.smallgdpr::lang.settings.form_fields.set_cookies_with_locale'
            comment: 'janvince.smallgdpr::lang.settings.form_fields.set_cookies_with_locale_comment'
            default: 1
            type: checkbox
            span: full
            tab: 'janvince.smallgdpr::lang.settings.tabs.settings'


        ## Import

        import_preset_media:
            label: 'janvince.smallgdpr::lang.settings.form_fields.import_path_media'
            comment: 'janvince.smallgdpr::lang.settings.form_fields.import_path_media_description'
            type: mediafinder
            span: left
            mode: file
            tab: 'janvince.smallgdpr::lang.settings.tabs.import'
        import_preset_btn:
            type: importpreset
            span: full
            tab: 'janvince.smallgdpr::lang.settings.tabs.import'
        _path_to_import:
            type: section
            label: Pad naar import instellingen
            comment: '/plugins/invato/settings/assets/configs/cookiedata.yaml'
            tab: 'janvince.smallgdpr::lang.settings.tabs.import'

        ## Export

        export_preset_btn:
            type: exportpreset
            span: left
            tab: 'janvince.smallgdpr::lang.settings.tabs.export'
