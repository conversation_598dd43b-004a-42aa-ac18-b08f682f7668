<?php namespace Invato\Blog\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoBlogPost4 extends Migration
{
    public function up()
    {
        Schema::table('invato_blog_post', function($table)
        {
            $table->text('additional_content')->nullable()->after('content');
            $table->text('img_title')->nullable()->after('image');
            $table->text('thumb_img_title')->nullable()->after('thumbnail_image');
        });
    }

    public function down()
    {
        Schema::table('invato_blog_post', function($table)
        {
            $table->dropColumn('additional_content');
            $table->dropColumn('img_title');
            $table->dropColumn('thumb_img_title');
        });
    }
}
