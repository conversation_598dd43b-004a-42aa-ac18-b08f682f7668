<?php namespace Invato\Notifications\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoNotificationsPopups extends Migration
{
    public function up()
    {
        Schema::table('invato_notifications_popups', function($table)
        {
            $table->text('button')->nullable();
        });
    }
    
    public function down()
    {
        Schema::table('invato_notifications_popups', function($table)
        {
            $table->dropColumn('button');
        });
    }
}
