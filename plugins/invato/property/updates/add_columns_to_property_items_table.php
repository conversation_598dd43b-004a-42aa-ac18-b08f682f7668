<?php

namespace Invato\Property\Updates;

use October\Rain\Database\Updates\Migration;
use Schema;

class AddColumnsToPropertyItemsTable extends Migration
{
    protected string $table = 'invato_property_items';

    public function up()
    {
        Schema::table($this->table, function($table)
        {
            $table->timestamp('synced_at')->nullable()->after('online_at');
            $table->timestamp('deleted_at')->nullable()->after('updated_at');
        });
    }

    public function down()
    {
        Schema::table($this->table, function($table)
        {
            $table->dropColumn('synced_at');
            $table->dropColumn('deleted_at');
        });
    }
}
