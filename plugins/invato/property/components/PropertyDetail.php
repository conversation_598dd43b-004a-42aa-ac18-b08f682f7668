<?php namespace Invato\Property\Components;

use Cms\Classes\ComponentBase;
use Invato\Property\Models\PropertyItem;

class PropertyDetail extends ComponentBase
{
    public $property;

    public function componentDetails()
    {
        return [
            'name' => 'Property Detail',
            'description' => 'Displays a single property'
        ];
    }

    public function defineProperties()
    {
        return [
            'slug' => [
                'title'       => 'Property slug',
                'description' => 'Enter post slug',
                'default'     => '{{ :slug }}',
                'type'        => 'string',
            ],
        ];
    }

    public function onRun()
    {
        $this->property = $this->getProperty();
    }

    protected function getProperty()
    {
      $slug = $this->property('slug');
      $property = new PropertyItem;
      $query = $property->query();
      $query->where('external_id', $slug);
      $property = $query->first();

      return $property;
    }
}
