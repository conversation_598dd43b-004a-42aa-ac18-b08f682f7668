<?php

namespace Invato\Woningscan\components;

use Cms\Classes\ComponentBase;
use Input;
use Invato\Woningscan\models\WoningscanSettings;
use Redirect;
use Session;

class Woningscan extends ComponentBase
{
    public $intro_title;
    public $intro_subtitle;
    public $intro_content;
    public $intro_button_text;
    public $result_title;
    public $result_text;
    public $advices;
    public $contact_text;

    public function componentDetails()
    {
        return [
            'name' => 'Woningscan',
            'description' => 'Show heating pumps after form submission',
        ];
    }

    public function defineProperties()
    {
        return [];
    }

    public function onRun()
    {
        $this->intro_title = WoningscanSettings::get('intro_title');
        $this->intro_subtitle = WoningscanSettings::get('intro_subtitle');
        $this->intro_content = WoningscanSettings::get('intro_content');
        $this->intro_button_text = WoningscanSettings::get('intro_button_text');
        $this->result_title = $this->page['result_title'] = WoningscanSettings::get('result_title');
        $this->result_text = $this->page['result_text'] = WoningscanSettings::get('result_text');
        $this->advices = $this->page['advices'] = WoningscanSettings::get('advices');
        $this->contact_text = $this->page['contact_text'] = WoningscanSettings::get('contact_text');
    }

    public function onSubmit()
    {
        $results = Input::post();
        Session::put('submission', $results);
        $this->result_title = $this->page['result_title'] = WoningscanSettings::get('result_title');
        $this->result_text = $this->page['result_text'] = WoningscanSettings::get('result_text');
        $this->advices = $this->page['advices'] = WoningscanSettings::get('advices');
        $this->contact_text = $this->page['contact_text'] = WoningscanSettings::get('contact_text');
    }

    public function onReset()
    {
        Session::forget('submission');

        return Redirect::refresh();
    }
}
