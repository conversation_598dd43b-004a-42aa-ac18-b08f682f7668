{% if pumps and not wpcheck_result %}
    <div class="space-y-12 md:space-y-0 md:grid md:grid-cols-2 md:gap-12">
        {% for item in pumps %}
            {% set investment = item.price - item.subsidy %}

            <div class="overflow-hidden bg-gray-50 shadow-sm sm:rounded-md border">
                <div class="fs-body">
                    <h4 class="text-lg font-semibold leading-8 tracking-tight text-primary-600 mb-0">Rekenvoorbeeld</h4>
                    <h2 class="text-lg font-medium leading-6 text-gray-900 lg:text-3xl">Warmtepomp van <span class="font-bold">{{ item.brand }}</span></h2>

                    <dl class="mt-6 space-y-4">

                        <div class="flex items-center justify-between">
                            <dt class="text-sm text-gray-600">Installatie</dt>
                            <dd class="text-sm font-medium text-gray-900">{{ format.euro(item.price) }}</dd>
                        </div>

                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <dt class="flex items-center text-sm text-gray-600">
                                <span>Subsidie</span>
                            </dt>
                            <dd class="text-sm font-medium text-gray-900">{{ format.euro(item.subsidy) }}</dd>
                        </div>

                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <dt class="text-base font-medium text-gray-900">Netto investering</dt>
                            <dd class="text-base font-medium text-gray-900">{{ format.euro(investment) }} </dd>
                        </div>
                    </dl>

                    <div class="text-xl font-bold text-gray-900 mt-12">Uw besparing</div>

                    <dl class="mt-6 space-y-4">
                        <div class="flex items-center justify-between">
                            <dt class="flex text-sm text-gray-600">
                                <span>Gasprijs (m3)</span>
                            </dt>
                            <dd class="text-sm font-medium text-gray-900">{{ format.euro(this.session.get('calculation').gasPrice) }}</dd>
                        </div>
                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <dt class="flex text-sm text-gray-600">
                                <span>Elektra (kWh)</span>
                            </dt>
                            <dd class="text-sm font-medium text-gray-900">{{ format.euro(this.session.get('calculation').energyPrice) }}</dd>
                        </div>
                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <dt class="flex text-sm text-gray-600">
                                <span>Besparing op gasverbruik</span>
                            </dt>
                            <dd class="text-sm font-medium text-gray-900">{{ item.gasSavings }}m³ p/j</dd>
                        </div>
                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <dt class="flex text-sm text-gray-600">
                                <span>Besparing op uw gasrekening</span>
                            </dt>
                            <dd class="text-sm font-medium text-gray-900">
                                {% set gasbillSavings = this.session.get('calculation').gasPrice * item.gasSavings %}
                                {{ format.euro(gasbillSavings) }} p/j
                            </dd>
                        </div>
                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <dt class="flex text-sm text-gray-600">
                                <span>Energieverbruik</span>
                            </dt>
                            <dd class="text-sm font-medium text-gray-900">{{ item.energyUsage }}kWh p/j</dd>
                        </div>
                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <dt class="flex text-sm text-gray-600">
                            <span>Energiekosten</span>
                            </dt>
                            <dd class="text-sm font-medium text-gray-900">
                                {% set energyCosts = this.session.get('calculation').energyPrice * item.energyUsage %}
                                {{ format.euro(energyCosts) }} p/j
                            </dd>
                        </div>
                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <dt class="text-base font-medium text-gray-900">
                                Totale besparing per jaar <br>
                                <small class="font-normal text-gray-600">{{ format.euro(gasbillSavings) }} - {{ format.euro(energyCosts) }}</small>
                            </dt>
                            <dd class="text-base font-medium text-gray-900">
                                {% set totalSavings = gasbillSavings - energyCosts %}
                                {{ format.euro(totalSavings) }}
                            </dd>
                        </div>
                        <div class="flex items-center justify-between border-t border-gray-200 pt-4">
                            <dt class="text-base font-medium text-gray-900">Terugverdientijd</dt>
                            <dd class="text-base font-medium text-gray-900">
                                {% set returnTime = investment / totalSavings %}
                                {{ returnTime | number_format }} jaar
                            </dd>
                        </div>
                    </dl>
                </div>
                <div class="fs-body border-t">
                    <div class="flex flex-wrap justify-between">
                        <a
                            href="#"
                            class="inline-flex items-center rounded-md border border-transparent bg-orange-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:bg-gray-400">
                            Offerte aanvragen
                        </a>

                        <a
                            href="{{ item.brochure }}"
                            class="inline-flex items-center rounded-md border border-transparent bg-orange-100 px-4 py-2 text-base font-medium text-orange-700 hover:bg-orange-200 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                            target="_blank">
                            Brochure bekijken
                        </a>

                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% endif %}
