<?php

namespace Invato\Seo\updates;

use Invato\Portfolio\Models\Category;
use Invato\Portfolio\Models\Project;
use October\Rain\Database\Updates\Migration;
use System\Classes\PluginManager;

return new class extends Migration
{
    public function up()
    {
        if (PluginManager::instance()->hasPlugin('Invato.Portfolio')) {
            // Portfolio projects
            Project::withTrashed()
                ->get()
                ->each(function ($record) {
                    $record->seo;
                });

            // Portfolio categories
            Category::withTrashed()
                ->get()
                ->each(function ($record) {
                    $record->seo;
                });
        }
    }
};
