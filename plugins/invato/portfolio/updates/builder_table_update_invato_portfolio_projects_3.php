<?php namespace Invato\Portfolio\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoPortfolioProjects3 extends Migration
{
    public function up()
    {
        Schema::table('invato_portfolio_projects', function($table)
        {
            $table->text('details')->nullable()->after('description');
            $table->text('custom_options')->nullable()->after('images');
        });
    }
    
    public function down()
    {
        Schema::table('invato_portfolio_projects', function($table)
        {
            $table->dropColumn('details');
            $table->dropColumn('custom_options');
        });
    }
}
