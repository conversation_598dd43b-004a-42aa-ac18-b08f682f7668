<?php

namespace Invato\Portfolio\Controllers;

use Backend\Behaviors\FormController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;

class Projects extends Controller
{
    public $implement = [
        FormController::class,
        ListController::class,
    ];

    public $formConfig = 'config_form.yaml';

    public $listConfig = 'config_list.yaml';

    public $requiredPermissions = [
        'invato.portfolio.manage_projects',
        'invato.portfolio.manage_plugin',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Portfolio', 'main-menu-item', 'side-menu-item2');
    }
}
