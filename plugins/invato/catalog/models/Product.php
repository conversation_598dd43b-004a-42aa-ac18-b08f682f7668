<?php

namespace Invato\Catalog\Models;

use Invato\Catalog\Components\ProductDetail;
use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Seo\traits\HasSeoableTrait;
use Model;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;

class Product extends Model
{
    // Begin Skeleton model
    use CanRedirectModelTrait;
    use HasSeoableTrait;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected static function booted(): void
    {
        static::deleting(static function ($product) {
            static::createRedirect(
                plugin: 'catalog',
                modelRecord: $product,
                detailPageController: ProductDetail::class,
                status: 301
            );
        });

        static::restored(static function ($product) {
            static::deleteRedirect($product);
        });
    }

    public $table = 'invato_catalog_products';

    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    protected $casts = [
        'id' => 'integer',
        'title' => 'string',
        'slug' => 'string',
        'thumbnail' => 'string',
        'excerpt' => 'string',
        'description' => 'string',
        'price' => 'decimal:2',
        'price_per_month' => 'decimal:2',
        'product_type' => 'string',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'title',
        'slug',
        'images',
        'thumbnail',
        'excerpt',
        'description',
        'usps',
        'price',
        'price_per_month',
        'product_type',
        'specifications',
        'sort_order',
        'custom_options',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // https://laravel.com/docs/10.x/validation#available-validation-rules
    public $rules = [
        'title' => ['required', 'string', 'max:255'],
        'slug' => ['required', 'string', 'max:255', 'unique:invato_catalog_products,slug,{{id}}'],
        'thumbnail' => ['nullable', 'string', 'max:255'],
        'excerpt' => ['nullable', 'string'],
        'description' => ['nullable', 'string'],
        'price' => ['required', 'numeric', 'decimal:0,2'],
        'price_per_month' => ['nullable', 'numeric', 'decimal:0,2'],
        'product_type' => ['nullable', 'string', 'max:255'],
        'sort_order' => ['integer'],
    ];

    protected array $slugs = [
        'slug' => 'title',
    ];

    // These attributes should not be in $casts and $rules
    protected $jsonable = [
        'specifications',
        'usps',
        'images',
        'custom_options',
    ];

    public $belongsToMany = [
        'categories' => [
            Category::class,
            'table' => 'invato_catalog_cat_prod',
        ],
    ];

    // END Skeleton model

    // BEGIN Model specific

    /**
     * Accessor for kopmaat from custom_options JSON field
     */
    public function getKopmaatAttribute()
    {
        return $this->custom_options['kopmaat'] ?? null;
    }

    /**
     * Accessor for lengte from custom_options JSON field
     */
    public function getLengteAttribute()
    {
        return $this->custom_options['lengte'] ?? null;
    }

    // END Model specific
}
