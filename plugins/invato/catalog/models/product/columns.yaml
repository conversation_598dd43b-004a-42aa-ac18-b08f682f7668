columns:
    title:
        label: 'invato.catalog::lang.fields.title'
        type: text
        searchable: true
        sortable: true
    slug:
        label: 'invato.catalog::lang.fields.slug'
        type: text
        searchable: true
        sortable: true
    price:
        label: 'invato.catalog::lang.fields.price'
        type: number
        sortable: true
    price_per_month:
        label: 'invato.catalog::lang.fields.price_per_month'
        type: number
        sortable: true
    created_at:
        label: 'Created at'
        type: date
        sortable: true
        format: d-m-Y
    updated_at:
        label: 'Updated at'
        type: date
        sortable: true
        format: d-m-Y
    # Deleted at only visible when filtering, with trashed is enabled
    deleted_at:
        label: 'Deleted at'
        invisible: true
    actions:
        label: 'Actions'
        type: partial
        path: column_actions
        clickable: false
        sortable: false
        align: center
        cssClass: 'column-button'
        width: '110px'
