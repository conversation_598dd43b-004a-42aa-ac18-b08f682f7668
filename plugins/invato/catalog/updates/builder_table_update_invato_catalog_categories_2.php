<?php namespace Invato\Catalog\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInvatoCatalogCategories2 extends Migration
{
    public function up()
    {
        Schema::table('invato_catalog_categories', function($table)
        {
            $table->text('image')->nullable()->after('slug');
            $table->string('img_title', 255)->nullable()->after('image');
        });
    }
    
    public function down()
    {
        Schema::table('invato_catalog_categories', function($table)
        {
            $table->dropColumn('image');
            $table->dropColumn('img_title');
        });
    }
}
