list: $/invato/courses/models/location/columns.yaml
modelClass: Invato\Courses\Models\Location
title: Locations
noRecordsMessage: 'backend::lang.list.no_records'
showSetup: true
showCheckboxes: true
recordsPerPage: 20
structure:
    showTree: false
    showReorder: true
toolbar:
    buttons: list_toolbar
    search:
        prompt: 'backend::lang.list.search_prompt'
recordUrl: 'invato/courses/locations/update/:id'
