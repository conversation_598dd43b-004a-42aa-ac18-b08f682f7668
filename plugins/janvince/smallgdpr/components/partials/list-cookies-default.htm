{% if cookiesSettingsGet('cookies', null)|length %}

    <form class="list-cookies">

        {% for cookie in cookiesSettingsGet('cookies') %}

            <label>

                <input type="checkbox" id="sg-cookies-{{ sgCookiesLocalePrefix ? sgCookiesLocalePrefix~'-' }}{{cookie.slug}}{{customId}}" {{ sgCookies[cookie.slug] or (cookie.required) ? 'checked' : '' }} {{ cookie.required ? 'disabled' : ''  }}> <strong>{{ cookie.title }}</strong>
            
            </label>
                    
            {% if cookie.description %}

                <span class="description">{{ cookie.description|raw }}</span>

            {% endif %}

        {% endfor %}

        {% if disableSaveButton is empty %}

            <button type="button" 
                    id="cookies-manage-save">{{ 'janvince.smallgdpr::lang.settings.form_fields.save_settings'|trans }}</button>

        {% endif %}

    </form>

{% endif %}
