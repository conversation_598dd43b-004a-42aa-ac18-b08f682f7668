# Backend Skin

The Backend Skin Plugin will help you changing all backend ui for octobercms.

You can change all backend matters, From core backend pages to plugin pages.

You can also change all assets.

> **Note:** This will not change the original file of octobercms.

### Why use the plugin?

Most of us want our admin dashboard to be look different.
It also help us to get more clients.

And you can change everything without changing the original file of octobercms.

### How to get start?

Getting started is just simple.
Simple because all you need is to add **backend** folder in your current theme.
Or you can add **backendskin/<yourskin>** in root directory

Your next step is to got documentation.
