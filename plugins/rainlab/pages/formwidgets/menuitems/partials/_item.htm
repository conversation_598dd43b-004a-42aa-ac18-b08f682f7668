<?php
    $groupStatus = true;
?>
<li
    class="<?= $item->items ? 'has-subitems' : null ?>"
    data-status="<?= $groupStatus || !$item->items ? 'expanded' : 'collapsed' ?>"
    data-menu-item="<?= e(json_encode($item->toArray())) ?>"
>
    <div>
        <a href="#" data-menu-item-link>
            <span class="title"><?= e($item->title) ?></span>
            <span class="comment"><?= e($this->getReferenceDescription($item)) ?></span>
        </a>

        <ul class="submenu">
            <li>
                <p>
                    <a href="javascript:;" data-control="create-item">
                        <i class="icon-plus"></i> <?= trans(e($this->addSubitemLabel)) ?>
                    </a>
                </p>
            </li>
            <li>
                <p>
                    <a href="javascript:;" data-control="delete-menu-item">
                        <i class="icon-trash-o control-icon"></i>
                    </a>
                </p>
            </li>
        </ul>
    </div>

    <ol>
        <?php if ($subItems = $item->items): ?>
            <?= $this->makePartial('itemlist', ['items' => $subItems]) ?>
        <?php endif ?>
    </ol>
</li>