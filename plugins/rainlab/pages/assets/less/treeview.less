@import "../../../../../modules/backend/assets/less/core/boot.less";

@color-treeview-item-bg:                     var(--oc-primary-bg, #ffffff);
@color-treeview-item-title:                  var(--oc-primary-color, #2b3e50);
@color-treeview-item-comment:                #95a5a6;
@color-treeview-control:                     #bdc3c7;
@color-treeview-hover-bg:                    var(--bs-primary, #6a6cf7);
@color-treeview-hover-text:                  #fff;
@color-treeview-active-bg:                   var(--oc-selection, #5254f6);
@color-treeview-active-text:                 #ffffff;
@color-treeview-item-active-comment:         #ffffff;
@color-treeview-light-submenu-bg:            #8284f8;
@color-treeview-light-submenu-border:        var(--bs-primary, #6a6cf7);
@color-treeview-cb-border:                   #cccccc;
@color-filelist-norecords-text:              #666666;

@font-size-base:          14px;

.control-treeview {
    margin-bottom: 40px;

    .no-data() {
        padding: 18px 0;
        margin: 0;
        color: @color-filelist-norecords-text;
        font-size: @font-size-base;
        text-align: center;
        font-weight: 400;
    }

    ol {
        margin: 0;
        padding: 0;
        list-style: none;
        background: @color-treeview-item-bg;

        > li {
            .transition(width 1s);

            > div {
                font-size: @font-size-base;
                font-weight: normal;
                background: @color-treeview-item-bg;
                border-bottom: 1px solid @tertiary-bg;
                position: relative;

                > a {
                    color: @color-treeview-item-title;
                    padding: 11px 45px 10px 61px;
                    display: block;
                    line-height: 150%;
                    text-decoration: none;
                    .box-sizing(border-box);
                }

                &:before {
                    content: ' ';
                    background-image: url(../images/treeview-icons.png);
                    background-position: 0px -28px;
                    background-repeat: no-repeat;
                    background-size: 42px auto;

                    position: absolute;
                    width: 21px;
                    height: 22px;
                    left: 28px;
                    top: 15px;
                }

                span.comment {
                    display: block;
                    font-weight: 400;
                    color: @color-treeview-item-comment;
                    font-size: @font-size-base - 1;
                    margin-top: 2px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                > span.expand {
                    .hide-text();
                    display: none;
                    position: absolute;
                    width: 20px;
                    height: 20px;
                    top: 19px;
                    left: 2px;
                    cursor: pointer;
                    color: @color-treeview-control;
                    .transition(transform 0.1s ease);

                    &:before {
                        .icon(@icon-caret-right);
                        font-family: FontAwesome, 'octo-icon';
                        line-height: 100%;
                        font-size: @font-size-base + 1;

                        position: relative;
                        left: 8px;
                        top: 2px;
                    }
                }

                > span.drag-handle {
                    .hide-text();
                    .transition(opacity 0.4s);

                    position: absolute;
                    right: 9px;
                    bottom: 0;
                    width: 18px;
                    height: 19px;
                    cursor: move;
                    color: @color-treeview-control;
                    opacity: 0;

                    &:before {
                        .icon(@icon-bars);
                        font-family: FontAwesome, 'octo-icon';
                        font-size: 18px;
                        line-height: inherit;
                    }
                }

                span.borders {
                    font-size: 0;
                }

                > ul.submenu {
                    position: absolute;
                    left: 20px;
                    bottom: -26.9px;
                    padding: 0;
                    list-style: none;
                    z-index: 200;
                    height: 27px;
                    display: none;
                    margin-left: 15px;
                    background-color: #7476f8;
                    .border-bottom-radius(8px);
                    .box-shadow(~"inset 0 3px 3px -3px rgba(0, 0, 0, 0.2)");

                    [data-control="create-object"] {
                        padding-left: 15px;
                        padding-right: 15px;
                    }

                    li {
                        font-size: @font-size-base - 2;

                        a {
                            display: block;
                            padding: 4px 3px 0 3px;
                            color: #fff;
                            text-decoration: none;
                            outline: none;

                            i {
                                margin-right: 5px;
                            }
                        }
                    }
                }

                &:hover {
                    > ul.submenu {
                        display: block;
                    }
                }

                &:active {
                    > ul.submenu {
                        background-color: @color-treeview-active-bg;
                    }
                }

                .checkbox {
                    position: absolute;
                    top: -2px;
                    right: 0;

                    label {
                        margin-right: 0;

                        &:before {
                            border-color: @color-treeview-cb-border;
                        }
                    }
                }

                &.popover-highlight {
                    background-color: @color-treeview-hover-bg !important;

                    &:before {
                        background-position: 0px -80px;
                    }

                    > a {
                        color: @color-treeview-hover-text !important;
                        cursor: default;
                    }

                    span {
                        color: @color-treeview-hover-text !important;
                    }

                    > ul.submenu, > span.drag-handle {
                        display: none!important;
                    }
                }
            }

            &.dragged div, > div:hover {
                background-color: @color-treeview-hover-bg !important;

                > a {
                    color: @color-treeview-hover-text !important;
                }

                &:before {
                    background-position: 0px -80px;
                }

                &:after {
                    top: 0 !important;
                    bottom: 0 !important;
                }

                span {
                    color: @color-treeview-hover-text !important;

                    &.drag-handle {
                        cursor: move;
                        opacity: 1;
                    }

                    &.borders {
                        display: none;
                    }
                }
            }

            > div:active {
                background-color: @color-treeview-active-bg !important;

                > a {
                    color: @color-treeview-active-text !important;
                }
            }

            &[data-no-drag-mode] div:hover {
                span.drag-handle {
                    cursor: default !important;
                    opacity: .3 !important;
                }
            }

            &.dragged {
                li.has-subitems, &.has-subitems {
                    > div:before {
                        background-position: 0px -52px;
                    }
                }

                div > ul.submenu {
                    display: none!important;
                }
            }

            > ol {
                padding-left: 20px;
                padding-right: 20px;
            }

            &[data-status=collapsed] > ol {
                display: none;
            }

            &.has-subitems {
                > div {
                    &:before {
                        background-position: 0 0;
                        width: 23px;
                        height: 26px;
                        left: 26px;
                    }

                    &:hover, &.popover-highlight {
                        &:before { background-position: 0px -52px; }
                    }

                    span.expand {
                        display: block;
                    }
                }
            }

            &.placeholder {
                position: relative;
                opacity: .5;
            }

            &.dragged {
                position: absolute;
                z-index: 2000;
                opacity: .25;

                > div {
                    .border-radius(3px);
                }
            }

            &.drop-target {
                > div {
                    background-color: #2581b8!important;

                    > a {
                        color: @color-treeview-hover-text;
                        > span.comment {
                            color: @color-treeview-hover-text;
                        }
                    }

                    &:before {
                        background-position: 0px -80px;
                    }
                }

                &.has-subitems > div:before {
                    background-position: 0px -52px;
                }
            }

            &[data-status=expanded] > div > span.expand {
                .transform( ~'rotate(90deg) translate(0, 0)' );
            }

            &.drag-ghost {
                background-color: transparent;
                box-sizing: content-box;
            }

            &.active {
                > div {
                    background: @color-treeview-active-bg;

                    > a {
                        color: @color-treeview-item-active-comment;

                        > span.comment, > span.expand {
                            color: @color-treeview-item-active-comment;
                        }
                    }

                    > span.expand {
                        color: @color-treeview-item-active-comment;
                    }

                    > span.borders {
                        &:before, &:after {
                            content: ' ';
                            position: absolute;
                            width: 100%;
                            height: 1px;
                            display: block;
                            left: 0;
                            background-color: @color-treeview-active-bg;
                        }

                        &:before {top: -1px;}
                        &:after {bottom: -1px;}
                    }

                    &:before {
                        background-position: 0px -80px;
                    }
                }

                &.has-subitems > div:before {
                    background-position: 0px -52px;
                }
            }

            &.no-data {
                .no-data();
            }
        }

        @max-level: 10;

        .tree-view-paddings (@level) when (@level > 0) {
            > li {
                > ol {
                    > li > div {
                        margin-left: -20-(@max-level - @level)*20px;
                        margin-right: -20-(@max-level - @level)*20px;
                        padding-left: 61+(@max-level - @level + 1)*10px;

                        > a {
                            margin-left: -61-(@max-level - @level + 1)*10px;
                            padding-left: 61+(@max-level - @level + 1)*10px;
                        }

                        &:before {
                            margin-left: (@max-level - @level + 1)*10px;
                        }

                        > span.expand {
                            left: 2+(@max-level - @level + 1)*10px;
                        }
                    }

                    .tree-view-paddings(@level - 1);
                }
            }
        }

        .tree-view-paddings (@max-level);
    }

    p.no-data {
        .no-data();
    }

    a.menu-control {
        display: block;
        margin: 20px;
        padding: 13px 15px;
        border: dotted 2px #ebebeb;
        color: #bdc3c7;
        font-size: @font-size-base - 2;
        font-weight: 600;
        text-transform: uppercase;
        border-radius: 5px;
        vertical-align: middle;

        &:hover, &:focus {
            text-decoration: none;
            background-color: @color-treeview-hover-bg;
            color: @color-treeview-hover-text;
            border: none;
            padding: 15px 17px;
        }

        &:active {
            background: @color-treeview-active-bg;
            color: @color-treeview-active-text;
        }

        i {
            margin-right: 10px;
            font-size: 14px;
        }
    }

    /*
     * Light version of the treeview - transparent background, no bottom borders,
     * smaller paddings, inline submenu
     */
    &.treeview-light {
        margin-bottom: 0;
        margin-top: 20px;

        ol {
            background-color: transparent;
            > li {
                > div {
                    background-color: transparent;
                    border-bottom: none;

                    &:before {
                        top: 15px;
                    }

                    > a {
                        padding-top: 10px;
                        padding-bottom: 10px;
                    }

                    span.expand {
                        top: 19px;
                    }

                    > span.drag-handle {
                        top: 0;
                        right: 0;
                        bottom: auto;
                        height: 100%;
                        width: 60px;
                        background: @color-treeview-light-submenu-bg;
                        .transition(none)!important;

                        &:before {
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            margin-left: -6px;
                        }
                    }

                    > ul.submenu {
                        right: 60px;
                        left: auto;
                        bottom: auto;
                        top: 0;
                        height: 100%;
                        margin: 0;
                        background: transparent;
                        white-space: nowrap;
                        font-size: 0;

                        &:before, &:after {
                            display: none;
                        }

                        li {
                            height: 100%;
                            display: inline-block;
                            background: @color-treeview-light-submenu-bg;
                            border-right: 1px solid @color-treeview-light-submenu-border;

                            p {
                                display: table;
                                height: 100%;
                                padding: 0;
                                margin: 0;

                                a {
                                    display: table-cell;
                                    vertical-align: middle;
                                    height: 100%;
                                    padding: 0 20px;
                                    font-size: @font-size-base - 1;
                                    .box-sizing(border-box);

                                    i.control-icon {
                                        font-size: 22px;
                                        margin-right: 0;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

//
// Sorting guides
//

body.dragging .control-treeview {
    ol.dragging, ol.dragging ol {
        background: #ccc;
        padding-right: 20px;
        .transition(padding 1s);

        > li {
            > div {
                margin-right: 0;
                .transition(margin 1s);

                .custom-checkbox {
                    transition: opacity .5s;
                    opacity: 0;
                }
            }
        }
    }

    &.treeview-light {
        ol.dragging, ol.dragging ol {
            > li > div  {
                background-color: #f9f9f9;
            }
        }
    }
}

//
// Retina
//

@media only screen and (min--moz-device-pixel-ratio: 1.5), only screen and (-o-min-device-pixel-ratio: 3/2), only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-devicepixel-ratio: 1.5), only screen and (min-resolution: 1.5dppx) {
    .control-treeview {
        ol {
            > li {
                > div{
                    &:before {
                        background-position: 0px -79px;
                        background-size: 21px auto;
                    }
                }

                &.has-subitems {
                    > div {
                        &:before {background-position: 0px -52px;}
                        &:hover, &.popover-highlight {
                            &:before {background-position: 0px -102px;}
                        }
                    }

                    &.active > div {
                        &:before {background-position: 0px -102px;}
                    }
                }

                &.dragged > div, &.dragged li > div, > div:hover, &.active > div, > div.popover-highlight {
                    &:before {background-position: 0px -129px;}
                }

                &.dragged {
                    li.has-subitems, &.has-subitems {
                        > div:before {
                            background-position: 0px -102px;
                        }
                    }
                }

                &.drop-target {
                    > div:before {
                        background-position: 0px -129px;
                    }

                    &.has-subitems > div:before {
                        background-position: 0px -102px;
                    }
                }
            }
        }
    }
}
