<?php return [
  'plugin' => [
    'name' => '<PERSON><PERSON><PERSON><PERSON>',
    'description' => 'Funkcie pre správu stránok a menu.',
  ],
  'page' => [
    'menu_label' => 'Stránky',
    'template_title' => '%s Stránky',
    'delete_confirmation' => 'Naozaj chcete odstrániť vybrané stránky? Ak existujú nejaké podstr<PERSON>, budú taktiež odstránené.',
    'no_records' => 'Neboli nájdené žiadne stránky',
    'delete_confirm_single' => 'Naozaj chcete odstrániť túto stránku? Ak existujú nejak<PERSON> podstr<PERSON>, budú taktiež odstránené.',
    'new' => 'Nová stránka',
    'add_subpage' => 'Pridať podstránku',
    'invalid_url' => 'Neplatný formát URL adresy. URL by mala za<PERSON><PERSON> symbolom lomítka a môže obsahovať číslice, latinské písmená a nasledujúce znaky: _- /.',
    'url_not_unique' => 'Túto URL adresu už používa iná stránka.',
    'layout' => 'Layout',
    'layouts_not_found' => 'Žiadne layouty neboli nájdené',
    'saved' => 'Stránka bola úspešne uložená.',
    'tab' => 'Stránky',
    'manage_pages' => 'Správa stránok',
    'manage_menus' => 'Správa menu',
    'access_snippets' => 'Správa snippetov',
    'manage_content' => 'Správa obsahu',
  ],
  'menu' => [
    'menu_label' => 'Menu',
    'delete_confirmation' => 'Naozaj chcete odstrániť vybrané menu?',
    'no_records' => 'Neboli nájdené žiadne položky',
    'new' => 'Nové menu',
    'new_name' => 'Nové menu',
    'new_code' => 'nove-menu',
    'delete_confirm_single' => 'Naozaj chcete odstrániť toto menu?',
    'saved' => 'Menu bolo úspešne uložené',
    'name' => 'Názov',
    'code' => 'Kód',
    'items' => 'Položky menu',
    'add_subitem' => 'Pridať vnorenú položku',
    'code_required' => 'Pole kód je povinné.',
    'invalid_code' => 'Neplatný formát kódu. Kód môže obsahovať číslice, latinské písmená a nasledujúce znaky: _-',
  ],
  'menuitem' => [
    'title' => 'Názov',
    'editor_title' => 'Upraviť položku menu',
    'type' => 'Typ',
    'allow_nested_items' => 'Povoliť vnorené položky',
    'allow_nested_items_comment' => 'Vnorené položky môžu byť automaticky generované statickou stránkou alebo niektorými ďalšími typmi položiek',
    'url' => 'URL adresa',
    'reference' => 'Odkaz',
    'search_placeholder' => 'Prehľadať všetky odkazy...',
    'title_required' => 'Názov je povinný',
    'unknown_type' => 'Neznámy typ položky menu',
    'unnamed' => 'Nepomenovaná položka menu',
    'add_item' => 'Pridať <u>P</u>oložku',
    'new_item' => 'Nová položka menu',
    'replace' => 'Nahradiť túto položku jej generovanými vnorenými položkami',
    'replace_comment' => 'Zaškrtnite toto pole pokiaľ si prajete vnorené položky menu posunúť na rovnakú úroveň akú má táto položka. Samotná položka zostane skrytá.',
    'cms_page' => 'CMS stránka',
    'cms_page_comment' => 'Vyberte stránku, ktorá sa má otvoriť po kliknutí na položku v menu.',
    'reference_required' => 'Odkaz na položku menu je povinný.',
    'url_required' => 'Adresa URL je povinná',
    'cms_page_required' => 'Prosím vyberte CMS stránku',
    'display_tab' => 'Zobrazenie',
    'hidden' => 'Skrytá',
    'hidden_comment' => 'Skryť túto položku menu pre celú webovú stránku.',
    'attributes_tab' => 'Vlastnosti',
    'code' => 'Kód',
    'code_comment' => 'Zadajte kód položky menu ak k nej chcete pristupovať prostredníctvom API.',
    'css_class' => 'CSS trieda',
    'css_class_comment' => 'Zadajte názov CSS triedy, ktorá sa ma aplikovať pre túto položku menu.',
    'external_link' => 'Externý odkaz',
    'external_link_comment' => 'Otvoriť odkaz tejto položky menu v novom okne.',
    'static_page' => 'Statická stránka',
    'all_static_pages' => 'Všetky statické stránky',
  ],
  'content' => [
    'menu_label' => 'Obsah',
    'cant_save_to_dir' => 'Ukladanie súborov s obsahom do adresára statických stránok nie je povolené.',
  ],
  'sidebar' => [
    'add' => 'Pridať',
  ],
  'object' => [
    'invalid_type' => 'Neznámy typ objektu',
    'not_found' => 'Požadovaný objekt nebol nájdený.',
  ],
  'editor' => [
    'title' => 'Názov',
    'new_title' => 'Názov novej stránky',
    'content' => 'Obsah',
    'url' => 'URL adresa',
    'filename' => 'Názov súboru',
    'layout' => 'Layout',
    'description' => 'Popis',
    'preview' => 'Náhľad',
    'enter_fullscreen' => 'Zapnúť režim celej obrazovky',
    'exit_fullscreen' => 'Vypnúť režim celej obrazovky',
    'hidden' => 'Skrytá',
    'hidden_comment' => 'Skryté stránky sú prístupné iba prihláseným používateľom.',
    'navigation_hidden' => 'Skryť v menu',
    'navigation_hidden_comment' => 'Začiarknutím tohto poľa skryjete túto stránku z automaticky generovaných menu a navigačnej cesty.',
  ],
  'snippet' => [
    'menu_label' => 'Snippety',
  ],
  'component' => [
    'static_page_name' => 'Statická stránka',
    'static_page_description' => 'Zobrazí obsah statickej stránky',
    'static_page_use_content_name' => 'Použiť pole obsah stránky',
    'static_page_use_content_description' => 'Ak nie je začiarknuté, sekcia obsahu sa nezobrazí pri úprave statickej stránky. Obsah stránky bude určený výhradne prostredníctvom zástupcov a premenných.',
    'static_page_default_name' => 'Predvolený layout',
    'static_page_default_description' => 'Nastaví tento layout ako predvolený pre nové stránky',
    'static_page_child_layout_name' => 'Layout podstránky',
    'static_page_child_layout_description' => 'Layout ktorý sa má použiť ako predvolený pre všetky nové podstránky',
    'static_menu_name' => 'Statické menu',
    'static_menu_description' => 'Zobrazí menu na stránke',
    'static_menu_code_name' => 'Menu',
    'static_menu_code_description' => 'Zadajte kód menu, ktoré má komponent zobraziť.',
    'static_breadcrumbs_name' => 'Statická navigačná cesta',
    'static_breadcrumbs_description' => 'Zobrazí navigačnú cestu na stránke.',
  ],
];
