<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableCreateInstalwebKetelsThermostats extends Migration
{
    public function up()
    {
        Schema::create('instalweb_ketels_thermostats', function($table)
        {
            $table->engine = 'InnoDB';
            $table->increments('id')->unsigned();
            $table->timestamp('created_at')->nullable();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('deleted_at')->nullable();
            $table->smallInteger('sort_order')->nullable()->default(0);
            $table->smallInteger('merken_id');
            $table->string('title');
            $table->text('description')->nullable();
            $table->string('image');
            $table->decimal('price', 10, 0)->nullable();
            $table->string('brochure')->nullable();
            $table->string('manual')->nullable();
            $table->boolean('inactive')->nullable();
        });
    }
    
    public function down()
    {
        Schema::dropIfExists('instalweb_ketels_thermostats');
    }
}
