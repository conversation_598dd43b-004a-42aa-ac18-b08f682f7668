<?php namespace Instalweb\Ketels\Updates;

use Schema;
use October\Rain\Database\Updates\Migration;

class BuilderTableUpdateInstalwebKetelsKetels extends Migration
{
    public function up()
    {
        Schema::table('instalweb_ketels_ketels', function($table)
        {
            $table->string('brochure')->nullable();
            $table->string('thermostats')->nullable();
            $table->smallInteger('comfort_level');
            $table->string('type')->nullable();
            $table->string('tap_capacity_60')->nullable();
            $table->string('tap_capacity_40')->nullable();
            $table->string('capacity')->nullable();
            $table->string('weight')->nullable();
            $table->string('size')->nullable();
            $table->string('efficiency')->nullable();
            $table->boolean('a_label_pump')->nullable();
            $table->string('warranty_heat_exchanger')->nullable();
            $table->string('energy_label_small')->nullable();
            $table->string('energy_label_big')->nullable();
        });
    }
    
    public function down()
    {
        Schema::table('instalweb_ketels_ketels', function($table)
        {
            $table->dropColumn('brochure');
            $table->dropColumn('thermostats');
            $table->dropColumn('comfort_level');
            $table->dropColumn('type');
            $table->dropColumn('tap_capacity_60');
            $table->dropColumn('tap_capacity_40');
            $table->dropColumn('capacity');
            $table->dropColumn('weight');
            $table->dropColumn('size');
            $table->dropColumn('efficiency');
            $table->dropColumn('a_label_pump');
            $table->dropColumn('warranty_heat_exchanger');
            $table->dropColumn('energy_label_small');
            $table->dropColumn('energy_label_big');
        });
    }
}
