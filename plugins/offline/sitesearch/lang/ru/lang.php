<?php return [
    'plugin'            => [
        'name'                       => 'SiteSearch',
        'description'                => 'Глобальный поиск для вашего фронтенда',
        'author'                     => 'OFFLINE LLC',
        'manage_settings'            => 'Управление настройками SiteSearch',
        'manage_settings_permission' => 'Разрешить управлять настройками SiteSearch',
        'view_log_permission'        => 'Может просматривать журнал поисковых запросов',
    ],
    'settings'          => [
        'mark_results'               => 'Подсветка совпадений в результатах поиска',
        'mark_results_comment'       => 'Обернуть поисковый запрос в тэги <mark>',
        'log_queries'                => 'Журнал запросов',
        'log_queries_comment'        => 'Записывать все запросы в базу данных',
        'log_keep_days'              => 'Через сколько дней чистить логи',
        'log_keep_days_comment'      => 'Удалить старые логи из записей журнала через указанное количество дней (По умолчанию: 365)',
        'excerpt_length'             => 'Длина выборки текста',
        'excerpt_length_comment'     => 'Длина выборки текста, показываемого в результатах поиска.',
        'use_this_provider'          => 'Использовать этот провайдер',
        'use_this_provider_comment'  => 'Включить отображение результатов для этого провайдера',
        'provider_badge'             => 'Метка провайдера',
        'provider_badge_comment'     => 'Текст, отображаемый как метка провайдера в результатах поиска',
        'blog_posturl'               => 'Url поста из блога',
        'blog_posturl_comment'       => 'Указывайте только фиксированную часть URL без каких-либо динамических параметров',
        'blog_page'                  => 'Страница поста из блога',
        'blog_page_comment'          => 'Укажите страницу, используемую для вывода поста из блога. Это нужно для правильной генерации URL поста.',
        'album_page'                 => 'Страница альбома',
        'album_page_comment'         => 'Выберите страницу, используемую для отображения фотоальбома. Требуется для формирования URL альбомов.',
        'photo_page'                 => 'Страница фото',
        'photo_page_comment'         => 'Выберите страницу, используемую для отображения одной фотографии. Требуется для формирования URL фотографий.',
        'portfolio_itemurl'          => 'URL страницы с описанием портфолио',
        'portfolio_itemurl_comment'  => 'Указывайте только фиксированную часть URL без каких-либо динамических параметров',
        'brands_itemurl'             => 'URL страницы с описанием бренда',
        'brands_itemurl_comment'     => 'Указывайте только фиксированную часть URL без каких-либо динамических параметров',
        'showcase_itemurl'           => 'Url страницы деталей showcase',
        'showcase_itemurl_comment'   => 'Указывайте только фиксированную часть URL без каких-либо динамических параметров',
        'octoshop_itemurl'           => 'URL страницы с описанием товара',
        'octoshop_itemurl_comment'   => 'Указывайте только фиксированную часть URL без каких-либо динамических параметров',
        'octoshop_itemurl_badge'     => 'Товар',
        'snipcartshop_itemurl_badge' => 'Товар',
        'jkshop_itemurl'             => 'URL страницы с описанием товара',
        'jkshop_itemurl_comment'     => 'Указывайте только фиксированную часть URL без каких-либо динамических параметров',
        'jkshop_itemurl_badge'       => 'Товар',
        'experimental'               => 'Эксперементальная фитча":',
        'experimental_refer_to_docs' => 'Этот провайдер является эксперементальным! Пожалуйста, обратитесь <a target="_blank"
href="https://octobercms.com/plugin/offline-sitesearch#documentation">к документации</a> перед его использованием.',
        'news_page'                  => 'Страница новости',
        'news_page_comment'          => 'Выберите страницу, используемую для отображения отдельного новостного сообщения. Требуется для формирования URL-адреса новостей.',
    ],
    'searchResults'     => [
        'title'       => 'Результаты поиска',
        'description' => 'Отображает список результатов поиска',
        'properties'  => [
            'no_results'       => [
                'title'       => 'Сообщение об отсутствии результатов',
                'description' => 'Что показывать, когда ничего не найдено',
            ],
            'provider_badge'   => [
                'title'       => 'Показывать метку провайдера',
                'description' => 'Отображает имя поискового провайдера для каждого результата',
            ],
            'results_per_page' => [
                'title' => 'Результатов на странице',
            ],
            'visit_page'       => [
                'title'       => 'Ссылка Посетить страницу',
                'description' => 'Этот текс помещается как ссылка под каждым результатом поиска',
            ],
            'min_query_length'       => [
                'title'       => 'Min. длина запроса',
                'description' => 'Символов в поисковом запросе должно быть не меньше этого числа.',
            ],
        ],
    ],
    'searchInput'       => [
        'title'       => 'Ввод поиска',
        'description' => 'Отображает ввод поиска',
        'properties'  => [
            'use_auto_complete'          => [
                'title' => 'Искать во время набора текста',
            ],
            'auto_complete_result_count' => [
                'title' => 'Макс. автозаполнение результатов',
            ],
            'search_page'                => [
                'title'       => 'Страница результатов поиска',
                'description' => 'Ваш поисковый запрос будет отправлен на эту страницу.',
                'null_value'  => '-- Не показывать ссылку',
            ],
        ],
    ],
    'siteSearchInclude' => [
        'title'       => 'Включить в SiteSearch',
        'description' => 'Добавьте это на CMS-страницу, чтобы включить её содержимое в поиск',
    ],
    'log'               => [
        'id'           => 'ID',
        'description'  => 'Журнал всех поисковых запросов',
        'title'        => 'Поисковые запросы',
        'title_update' => 'Vie search query',
        'query'        => 'Запрос',
        'created_at'   => 'Создано',
        'domain'       => 'Домен',
        'location'     => 'Путь',
        'session_id'   => 'Сессия',
        'export'       => 'Export log',
        'useragent'    => 'User agent',
    ],
    'query_too_short' => 'Пожалуйста, введите не менее :min символов.'
];
